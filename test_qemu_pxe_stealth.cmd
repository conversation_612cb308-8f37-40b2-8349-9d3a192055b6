@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM QEMU PXE Boot Test Script - Maximum Stealth Anti-Detection
REM Based on techniques from:
REM - https://github.com/zhaodice/qemu-anti-detection
REM - https://github.com/A1exxander/KVM-Spoofing
REM 
REM Features:
REM - No disk/cdrom (PXE boot only)
REM - No USB or audio devices
REM - SMBIOS spoofing
REM - CPU feature masking
REM - VM signature hiding
REM ============================================================================

echo ============= QEMU PXE Boot - Maximum Stealth Mode =============
echo.
echo Anti-Detection Features:
echo - SMBIOS spoofing (BIOS, System, Board info)
echo - CPU model spoofing
echo - MAC address randomization
echo - VM signature hiding
echo - No USB/Audio devices
echo - PXE network boot only
echo.

REM Configuration variables
set VM_NAME=stealth-workstation
set VM_MEMORY=4096
set VM_CPUS=4
set QEMU_PATH=C:\Program Files\qemu\qemu-system-x86_64.exe

REM Check if QEMU is available
if not exist "%QEMU_PATH%" (
    echo Error: QEMU not found at %QEMU_PATH%
    echo Please install QEMU or update the path
    echo Download from: https://www.qemu.org/download/
    pause
    exit /b 1
)

echo [+] Using QEMU: %QEMU_PATH%

REM Generate realistic MAC address with Intel OUI
set /a "mac1=%random% %% 256"
set /a "mac2=%random% %% 256"
set /a "mac3=%random% %% 256"
call :tohex !mac1! hex1
call :tohex !mac2! hex2  
call :tohex !mac3! hex3
set MAC_ADDRESS=08:00:27:!hex1!:!hex2!:!hex3!

echo [+] Generated MAC address: %MAC_ADDRESS%
echo.

REM Generate random serial numbers
set /a "serial1=%random% %% 10000000"
set /a "serial2=%random% %% 10000000"
set SYSTEM_SERIAL=SYS!serial1!
set BOARD_SERIAL=BSN!serial2!

echo [+] Generated System Serial: %SYSTEM_SERIAL%
echo [+] Generated Board Serial: %BOARD_SERIAL%
echo.

REM Build stealth QEMU command
set QEMU_CMD="%QEMU_PATH%"
set QEMU_CMD=%QEMU_CMD% -name "%VM_NAME%"
set QEMU_CMD=%QEMU_CMD% -m %VM_MEMORY%
set QEMU_CMD=%QEMU_CMD% -smp %VM_CPUS%,cores=%VM_CPUS%,threads=1,sockets=1

REM Machine configuration with anti-detection
set QEMU_CMD=%QEMU_CMD% -machine pc-q35-7.2,accel=tcg,kernel_irqchip=on

REM CPU spoofing - mimic real Intel CPU
set QEMU_CMD=%QEMU_CMD% -cpu qemu64,family=6,model=158,stepping=10
set QEMU_CMD=%QEMU_CMD% -cpu qemu64,model_id="Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz"
set QEMU_CMD=%QEMU_CMD% -cpu qemu64,vmware-cpuid-freq=off,hypervisor=off

REM SMBIOS Type 0 - BIOS Information
set QEMU_CMD=%QEMU_CMD% -smbios type=0,vendor="American Megatrends Inc."
set QEMU_CMD=%QEMU_CMD% -smbios type=0,version="2603",date="04/09/2021"

REM SMBIOS Type 1 - System Information
set QEMU_CMD=%QEMU_CMD% -smbios type=1,manufacturer="ASUSTeK COMPUTER INC."
set QEMU_CMD=%QEMU_CMD% -smbios type=1,product="PRIME Z390-A",version="Rev 1.xx"
set QEMU_CMD=%QEMU_CMD% -smbios type=1,serial="%SYSTEM_SERIAL%"
set QEMU_CMD=%QEMU_CMD% -smbios type=1,uuid="550e8400-e29b-41d4-a716-************"
set QEMU_CMD=%QEMU_CMD% -smbios type=1,sku="SKU",family="Desktop"

REM SMBIOS Type 2 - Baseboard Information
set QEMU_CMD=%QEMU_CMD% -smbios type=2,manufacturer="ASUSTeK COMPUTER INC."
set QEMU_CMD=%QEMU_CMD% -smbios type=2,product="PRIME Z390-A",version="Rev 1.xx"
set QEMU_CMD=%QEMU_CMD% -smbios type=2,serial="%BOARD_SERIAL%"

REM SMBIOS Type 3 - Chassis Information
set QEMU_CMD=%QEMU_CMD% -smbios type=3,manufacturer="Chassis Manufacture"
set QEMU_CMD=%QEMU_CMD% -smbios type=3,version="Chassis Version"
set QEMU_CMD=%QEMU_CMD% -smbios type=3,serial="Chassis Serial Number"
set QEMU_CMD=%QEMU_CMD% -smbios type=3,asset="Asset-1234567890"

REM SMBIOS Type 4 - Processor Information
set QEMU_CMD=%QEMU_CMD% -smbios type=4,manufacturer="Intel"
set QEMU_CMD=%QEMU_CMD% -smbios type=4,version="Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz"
set QEMU_CMD=%QEMU_CMD% -smbios type=4,max-speed=3600,current-speed=3600

REM Timing and clock
set QEMU_CMD=%QEMU_CMD% -rtc base=localtime,clock=host

REM Network configuration for real PXE boot
set QEMU_CMD=%QEMU_CMD% -boot order=n,menu=off,splash-time=0
set QEMU_CMD=%QEMU_CMD% -netdev user,id=net0
set QEMU_CMD=%QEMU_CMD% -device e1000,netdev=net0,mac=%MAC_ADDRESS%

REM Graphics - standard VGA to avoid VM-specific drivers
set QEMU_CMD=%QEMU_CMD% -vga std
set QEMU_CMD=%QEMU_CMD% -display gtk,grab-on-hover=on

REM Disable VM-specific features and devices
set QEMU_CMD=%QEMU_CMD% -no-hpet
set QEMU_CMD=%QEMU_CMD% -no-acpi
set QEMU_CMD=%QEMU_CMD% -no-reboot

REM Monitoring
set QEMU_CMD=%QEMU_CMD% -monitor stdio

echo ============================================================================
echo Stealth QEMU Configuration Summary:
echo VM Name: %VM_NAME%
echo Memory: %VM_MEMORY% MB
echo CPUs: %VM_CPUS%
echo Machine: Q35 chipset with anti-detection
echo CPU: Intel i7-9700K (spoofed)
echo BIOS: American Megatrends Inc. (spoofed)
echo System: ASUS PRIME Z390-A (spoofed)
echo Network: Intel E1000 (%MAC_ADDRESS%)
echo Boot: PXE only (no storage devices)
echo Anti-Detection: SMBIOS spoofing, CPU masking, no VM signatures
echo ============================================================================
echo.

echo [+] VM configured for real PXE network boot
echo.

echo Starting stealth QEMU VM...
echo.
echo Instructions:
echo - VM will attempt real PXE boot from your network
echo - All VM signatures have been hidden/spoofed
echo - No USB or audio devices attached
echo - If no PXE server exists, you'll see boot failure (normal)
echo - Type 'quit' in this console to stop VM
echo - Press Ctrl+Alt+G to release mouse/keyboard
echo.

REM Log command for debugging
echo Full command:
echo %QEMU_CMD%
echo.

REM Start QEMU
%QEMU_CMD%

echo.
echo Stealth VM has stopped.
pause
goto :eof

REM Function to convert decimal to hex
:tohex
set /a "dec=%1"
set "hex="
set "digits=0123456789ABCDEF"
if %dec% equ 0 (
    set "hex=00"
    goto :tohex_end
)
:tohex_loop
if %dec% equ 0 goto :tohex_end
set /a "remainder=dec %% 16"
set /a "dec=dec / 16"
call set "digit=%%digits:~%remainder%,1%%"
set "hex=%digit%%hex%"
goto :tohex_loop
:tohex_end
if "%hex:~1,1%"=="" set "hex=0%hex%"
set "%2=%hex%"
goto :eof

endlocal
