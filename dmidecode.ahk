#NoEnv
#SingleInstance Force

; ============= DYNAMIC VIRTUALBOX ANTI-DETECTION SCRIPT =============
; This script detects current system hardware and applies VirtualBox anti-detection
; configuration directly to a specified VM using actual hardware specifications
; Based on VBoxHardenedLoader and VBoxAntiDetection GitHub repositories

; Get VM name from command line parameter or use default
if (A_Args.Length() >= 1) {
    VM_NAME := A_Args[1]
} else {
    VM_NAME := "vm3"  ; Default VM name
}

; Read VirtualBox installation path from vm3.ini
IniRead, VBoxPath, vm3.ini, vbox, vbox_path, C:\Program Files\Oracle\VirtualBox
if (VBoxPath = "ERROR") {
    MsgBox, 16, Error, Could not read VirtualBox path from vm3.ini.`nUsing default path: C:\Program Files\Oracle\VirtualBox
    VBoxPath := "C:\Program Files\Oracle\VirtualBox"
}

; Read additional configuration from vm3.ini
IniRead, VM_Memory, vm3.ini, vbox, vm_memory, 4096
IniRead, VM_Mouse, vm3.ini, vbox, vm_mouse, ps2
IniRead, VM_VGA, vm3.ini, vbox, vm_vga, vboxsvga
IniRead, VGA_VRAM, vm3.ini, vbox, vga_vram, 128

; Set VBoxManage path
vboxman := VBoxPath . "\VBoxManage.exe"

; Verify VBoxManage.exe exists
IfNotExist, %vboxman%
{
    MsgBox, 16, Error, VBoxManage.exe not found at:`n%vboxman%`n`nPlease check your VirtualBox installation path in vm3.ini
    ExitApp
}

; Display startup message
MsgBox, 64, Starting Process, Starting hardware detection and VirtualBox anti-detection configuration`n`nVM: %VM_NAME%`nVirtualBox Path: %VBoxPath%

; ============= HARDWARE DETECTION PHASE =============

; Copy dmidecode.exe to temp directory
If A_IsCompiled
	FileInstall, bin\dmidecode.exe, %A_Temp%\dmidecode.exe, 1
Else
	FileCopy, bin\dmidecode.exe, %A_Temp%, 1

; Run dmidecode.exe and capture output to dmidecode.txt with proper encoding
RunWait, %comspec% /c "chcp 65001 > nul && %A_Temp%\dmidecode.exe > %A_Temp%\dmidecode.txt", , Hide
if ErrorLevel {
    MsgBox, 16, Error, Failed to run dmidecode.exe. Please ensure:`n`n1. dmidecode.exe is in the same folder as this script`n2. You are running as Administrator`n3. dmidecode.exe is compatible with your system
    ExitApp
}

; Verify the file was created and has content
FileGetSize, fileSize, %A_Temp%\dmidecode.txt
if (ErrorLevel || fileSize = 0) {
    MsgBox, 16, Error, dmidecode.txt was not created or is empty.`nPlease check if dmidecode.exe ran successfully.
    ExitApp
}

; Read the generated dmidecode.txt file
FileRead, content, %A_Temp%\dmidecode.txt
if ErrorLevel {
    MsgBox, 16, Error, Error reading generated dmidecode.txt file
    ExitApp
}

; ============= GATHER ADDITIONAL SYSTEM INFORMATION USING WMIC =============

; Get system memory information
RunWait, %comspec% /c wmic computersystem get TotalPhysicalMemory /value > %A_Temp%\memory.txt, , Hide
FileRead, memoryInfo, %A_Temp%\memory.txt
RegExMatch(memoryInfo, "TotalPhysicalMemory=(\d+)", memMatch)
if (memMatch1) {
    ; Convert bytes to MB
    totalMemoryMB := Round(memMatch1 / 1024 / 1024)

    ; Assign half of system memory to VM, rounded to nearest common size
    halfMemoryMB := Round(totalMemoryMB / 2)

    ; Round to nearest common VM memory size, but be more generous for half-memory
    if (halfMemoryMB >= 10240)        ; >= 10GB (for 20GB+ systems)
        SystemMemory := 12288
    else if (halfMemoryMB >= 6144)    ; >= 6GB (for 12GB+ systems)
        SystemMemory := 8192
    else if (halfMemoryMB >= 3584)    ; >= 3.5GB (for 7GB+ systems)
        SystemMemory := 4096
    else if (halfMemoryMB >= 2560)    ; >= 2.5GB (for 5GB+ systems)
        SystemMemory := 3072
    else if (halfMemoryMB >= 1536)    ; >= 1.5GB (for 3GB+ systems)
        SystemMemory := 2048
    else if (halfMemoryMB >= 768)     ; >= 768MB (for 1.5GB+ systems)
        SystemMemory := 1024
    else
        SystemMemory := 512           ; Minimum 512MB
} else {
    SystemMemory := VM_Memory ; fallback to ini setting
}

; Get CPU core count from host system using environment variable
EnvGet, ProcessorCount, NUMBER_OF_PROCESSORS

if (ProcessorCount > 0) {
    SystemCPUCores := ProcessorCount
    SystemCPUThreads := ProcessorCount  ; For logging purposes
    VMCPUCount := ProcessorCount

    ; VBoxHardenedLoader requirement: MINIMUM 2 CPUs (malware detects single CPU as VM)
    if (VMCPUCount < 2)
        VMCPUCount := 2
    else if (VMCPUCount > 16)
        VMCPUCount := 16
} else {
    ; Fallback values - minimum 2 CPUs as per VBoxHardenedLoader
    SystemCPUCores := 4
    SystemCPUThreads := 4
    VMCPUCount := 4
}

; Get disk information using WMIC
RunWait, %comspec% /c wmic diskdrive get Model`,FirmwareRevision`,SerialNumber /format:csv > %A_Temp%\diskinfo.txt, , Hide
FileRead, diskInfo, %A_Temp%\diskinfo.txt

; Parse disk information
DiskModel := ""
DiskFirmware := ""
DiskSerial := ""
diskLines := StrSplit(diskInfo, "`n", "`r")
for index, line in diskLines {
    if (InStr(line, ",") && !InStr(line, "Node,")) {
        parts := StrSplit(line, ",")
        if (parts.Length() >= 4) {
            if (parts[2] != "" && parts[2] != "FirmwareRevision") {
                DiskFirmware := Trim(parts[2])
            }
            if (parts[3] != "" && parts[3] != "Model") {
                DiskModel := Trim(parts[3])
            }
            if (parts[4] != "" && parts[4] != "SerialNumber") {
                DiskSerial := Trim(parts[4])
            }
            if (DiskModel != "" && DiskFirmware != "" && DiskSerial != "")
                break
        }
    }
}

; ============= INITIALIZE HARDWARE DETECTION VARIABLES =============

; BIOS Information variables
BIOS_VENDOR := ""
BIOS_VERSION := ""
BIOS_DATE := ""
DmiBIOSReleaseMajor := "5"
DmiBIOSReleaseMinor := "13"
DmiBIOSFirmwareMajor := "1"
DmiBIOSFirmwareMinor := "0"

; System Information variables
SYS_MANUFACTURER := ""
SYS_PRODUCT := ""
SYS_VERSION := ""
SYS_SERIAL := ""
SYS_UUID := ""
SYS_SKU := ""
SYS_FAMILY := ""

; Board Information variables
BOARD_MANUFACTURER := ""
BOARD_PRODUCT := ""
BOARD_VERSION := ""
BOARD_SERIAL := ""
DmiBoardAssetTag := "Base Board Asset Tag#"
DmiBoardLocInChass := "Board Loc In"
DmiBoardBoardType := "10"

; Chassis Information variables
DmiChassisVendor := ""
DmiChassisType := "3"
DmiChassisVersion := ""
DmiChassisSerial := ""
DmiChassisAssetTag := "Desktop"

; Processor Information variables
DmiProcManufacturer := ""
DmiProcVersion := ""

; Storage device variables (will be overridden by WMIC if available)
HDD_MANUFACTURER := ""
HDD_FIRMWARE := ""
HDD_Serial := ""

; DVD/CD Drive variables
ATAPIVendorId := "Slimtype"
ATAPIProductId := "DVD A  DS8A8SH"
ATAPIRevision := "KAA2"

; Detection flags
HasHardDisk := false
HasDVDDrive := false

; ACPI variables
AcpiOemId := "GBT   "
AcpiCreatorId := "MSFT"
AcpiCreatorRev := "0x01000013"

; OEM Information
DmiOEMVBoxVer := "Extended version info: 1.00.00"
DmiOEMVBoxRev := "Extended revision info: 1A"

; ============= PARSE DMIDECODE OUTPUT =============

; Parse dmidecode.txt content line by line
Lines := StrSplit(content, "`n", "`r")
CurrentSection := ""
i := 1

while (i <= Lines.Length()) {
    line := Trim(Lines[i])

    ; Detect DMI sections using precise regex matching with word boundaries
    if (InStr(line, "Handle") && InStr(line, "DMI type")) {
        if (RegExMatch(line, "DMI type 0\b")) {
            CurrentSection := "BIOS"
        }
        else if (RegExMatch(line, "DMI type 1\b")) {
            CurrentSection := "System"
        }
        else if (RegExMatch(line, "DMI type 2\b")) {
            CurrentSection := "Board"
        }
        else if (RegExMatch(line, "DMI type 3\b")) {
            CurrentSection := "Chassis"
        }
        else if (RegExMatch(line, "DMI type 4\b")) {
            CurrentSection := "Processor"
        }
        else {
            CurrentSection := ""
        }
        i++
        continue
    }

    ; Reset section on empty lines (section boundaries)
    if (line = "") {
        ; Don't reset section on empty lines within sections
        i++
        continue
    }

    ; Parse BIOS Information
    if (CurrentSection = "BIOS") {
        if (InStr(line, "Vendor:")) {
            BIOS_VENDOR := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            BIOS_VERSION := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Release Date:")) {
            BIOS_DATE := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "BIOS Revision:")) {
            revision := Trim(SubStr(line, InStr(line, ":") + 1))
            if (InStr(revision, ".")) {
                parts := StrSplit(revision, ".")
                if (parts.Length() >= 2) {
                    DmiBIOSReleaseMajor := parts[1]
                    DmiBIOSReleaseMinor := parts[2]
                }
            }
        }
    }

    ; Parse System Information - only process if we're in the correct DMI type 1 section
    else if (CurrentSection = "System") {
        if (InStr(line, "Manufacturer:") && SYS_MANUFACTURER = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_MANUFACTURER := temp
        }
        else if (InStr(line, "Product Name:") && SYS_PRODUCT = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_PRODUCT := temp
        }
        else if (InStr(line, "Version:") && SYS_VERSION = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_VERSION := temp
        }
        else if (InStr(line, "Serial Number:") && SYS_SERIAL = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_SERIAL := temp
        }
        else if (InStr(line, "UUID:") && SYS_UUID = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_UUID := temp
        }
        else if (InStr(line, "SKU Number:") && SYS_SKU = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_SKU := temp
        }
        else if (InStr(line, "Family:") && SYS_FAMILY = "") {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_FAMILY := temp
        }
    }

    ; Parse Board Information
    else if (CurrentSection = "Board") {
        if (InStr(line, "Manufacturer:")) {
            BOARD_MANUFACTURER := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Product Name:")) {
            BOARD_PRODUCT := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            BOARD_VERSION := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Serial Number:")) {
            BOARD_SERIAL := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Asset Tag:")) {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified")
                DmiBoardAssetTag := temp
        }
        else if (InStr(line, "Location In Chassis:")) {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified")
                DmiBoardLocInChass := temp
        }
        else if (InStr(line, "Type:")) {
            temp := Trim(SubStr(line, InStr(line, ":") + 1))
            if (temp != "" && temp != "Not Specified")
                DmiBoardBoardType := 10
        }
    }
    
    ; Parse Chassis Information
    else if (CurrentSection = "Chassis") {
        if (InStr(line, "Manufacturer:")) {
            DmiChassisVendor := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Type:")) {
            DmiChassisType := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            DmiChassisVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Serial Number:")) {
            DmiChassisSerial := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Asset Tag:")) {
            DmiChassisAssetTag := Trim(SubStr(line, InStr(line, ":") + 1))
        }
    }
    
    ; Parse Processor Information
    else if (CurrentSection = "Processor") {
        if (InStr(line, "Manufacturer:")) {
            DmiProcManufacturer := Trim(SubStr(line, InStr(line, ":") + 1))
        }
        else if (InStr(line, "Version:")) {
            DmiProcVersion := Trim(SubStr(line, InStr(line, ":") + 1))
        }
    }
    
    i++
}

; Parse storage devices from dmidecode output
ModelNumber := ""
FirmwareRevision := ""
SerialNumber := ""
i := 1
while (i <= Lines.Length()) {
    line := Trim(Lines[i])

    ; Look for storage device information
    if (InStr(line, "Model Number:") && !HasHardDisk) {
        ModelNumber := Trim(SubStr(line, InStr(line, ":") + 1))
        if (ModelNumber != "" && ModelNumber != "Not Specified") {
            HasHardDisk := true
        }
    }
    else if (InStr(line, "Firmware Revision:") && HasHardDisk && FirmwareRevision = "") {
        FirmwareRevision := Trim(SubStr(line, InStr(line, ":") + 1))
    }
    else if (InStr(line, "Serial Number:") && HasHardDisk && SerialNumber = "") {
        temp := Trim(SubStr(line, InStr(line, ":") + 1))
        if (temp != "" && temp != "Not Specified") {
            SerialNumber := temp
        }
    }

    i++
}

; Parse ACPI information
i := 1
while (i <= Lines.Length()) {
    line := Trim(Lines[i])

    if (InStr(line, "OEM ID:")) {
        AcpiOemId := Trim(SubStr(line, InStr(line, ":") + 1))
    }

    i++
}

; ============= ENHANCE WITH WMIC SYSTEM INFORMATION =============

; Get additional system information from WMIC using individual commands to avoid parsing issues
if (SYS_MANUFACTURER = "" || SYS_MANUFACTURER = "Default string") {
    RunWait, %comspec% /c wmic computersystem get Manufacturer /value > %A_Temp%\manufacturer.txt, , Hide
    FileRead, mfgInfo, %A_Temp%\manufacturer.txt
    if (!ErrorLevel && mfgInfo != "") {
        if (RegExMatch(mfgInfo, "Manufacturer=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_MANUFACTURER := temp
        }
    }
}

if (SYS_PRODUCT = "" || SYS_PRODUCT = "Default string") {
    RunWait, %comspec% /c wmic computersystem get Model /value > %A_Temp%\model.txt, , Hide
    FileRead, modelInfo, %A_Temp%\model.txt
    if (!ErrorLevel && modelInfo != "") {
        if (RegExMatch(modelInfo, "Model=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_PRODUCT := temp
        }
    }
}

if (SYS_FAMILY = "" || SYS_FAMILY = "Default string") {
    RunWait, %comspec% /c wmic computersystem get SystemFamily /value > %A_Temp%\family.txt, , Hide
    FileRead, familyInfo, %A_Temp%\family.txt
    if (!ErrorLevel && familyInfo != "") {
        if (RegExMatch(familyInfo, "SystemFamily=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                SYS_FAMILY := temp
        }
    }
}

; Get BIOS information from WMIC
RunWait, %comspec% /c wmic bios get Manufacturer`,SMBIOSBIOSVersion`,ReleaseDate`,SerialNumber /format:csv > %A_Temp%\bios.txt, , Hide
FileRead, biosInfo, %A_Temp%\bios.txt
if (!ErrorLevel && biosInfo != "") {
    biosLines := StrSplit(biosInfo, "`n", "`r")
    for index, line in biosLines {
        if (InStr(line, ",") && !InStr(line, "Node,")) {
            parts := StrSplit(line, ",")
            if (parts.Length() >= 5) {
                ; WMIC CSV format: Node,Manufacturer,ReleaseDate,SerialNumber,SMBIOSBIOSVersion
                if (parts[2] != "" && parts[2] != "Manufacturer" && BIOS_VENDOR = "") {
                    BIOS_VENDOR := Trim(parts[2])
                }
                if (parts[5] != "" && parts[5] != "SMBIOSBIOSVersion" && BIOS_VERSION = "") {
                    BIOS_VERSION := Trim(parts[5])
                }
                if (parts[3] != "" && parts[3] != "ReleaseDate" && BIOS_DATE = "") {
                    ; Convert WMIC date format (YYYYMMDDHHMMSS.SSSSSS+ZZZ) to MM/DD/YYYY
                    wmicDate := Trim(parts[3])
                    if (StrLen(wmicDate) >= 8) {
                        year := SubStr(wmicDate, 1, 4)
                        month := SubStr(wmicDate, 5, 2)
                        day := SubStr(wmicDate, 7, 2)
                        BIOS_DATE := month . "/" . day . "/" . year
                    }
                }
                if (parts[4] != "" && parts[4] != "SerialNumber" && (SYS_SERIAL = "" || SYS_SERIAL = "Default string")) {
                    SYS_SERIAL := Trim(parts[4])
                }
                break
            }
        }
    }
}

; Get motherboard information from WMIC using individual /value commands
if (BOARD_MANUFACTURER = "" || BOARD_MANUFACTURER = "Default string") {
    RunWait, %comspec% /c wmic baseboard get Manufacturer /value > %A_Temp%\board_mfg.txt, , Hide
    FileRead, boardMfgInfo, %A_Temp%\board_mfg.txt
    if (!ErrorLevel && boardMfgInfo != "") {
        if (RegExMatch(boardMfgInfo, "Manufacturer=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                BOARD_MANUFACTURER := temp
        }
    }
}

if (BOARD_PRODUCT = "" || BOARD_PRODUCT = "Default string") {
    RunWait, %comspec% /c wmic baseboard get Product /value > %A_Temp%\board_prod.txt, , Hide
    FileRead, boardProdInfo, %A_Temp%\board_prod.txt
    if (!ErrorLevel && boardProdInfo != "") {
        if (RegExMatch(boardProdInfo, "Product=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                BOARD_PRODUCT := temp
        }
    }
}

if (BOARD_VERSION = "" || BOARD_VERSION = "x.x" || BOARD_VERSION = "Default string") {
    RunWait, %comspec% /c wmic baseboard get Version /value > %A_Temp%\board_ver.txt, , Hide
    FileRead, boardVerInfo, %A_Temp%\board_ver.txt
    if (!ErrorLevel && boardVerInfo != "") {
        if (RegExMatch(boardVerInfo, "Version=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                BOARD_VERSION := temp
        }
    }
}

if (BOARD_SERIAL = "" || BOARD_SERIAL = "Default string") {
    RunWait, %comspec% /c wmic baseboard get SerialNumber /value > %A_Temp%\board_ser.txt, , Hide
    FileRead, boardSerInfo, %A_Temp%\board_ser.txt
    if (!ErrorLevel && boardSerInfo != "") {
        if (RegExMatch(boardSerInfo, "SerialNumber=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified" && temp != "To Be Filled By O.E.M.")
                BOARD_SERIAL := temp
        }
    }
}

; ============= FINALIZE HARDWARE DETECTION =============

; Use detected disk information if available, otherwise use fallback values
if (DiskModel != "" && DiskFirmware != "" && DiskSerial != "") {
    HDD_MANUFACTURER := DiskModel
    HDD_FIRMWARE := DiskFirmware
    HDD_Serial := DiskSerial
    HasHardDisk := true
} else {
    ; Use fallback values if WMIC detection failed
    HDD_MANUFACTURER := "Hitachi HTS543230AAA384"
    HDD_FIRMWARE := "ES2OA60W"
    HDD_Serial := "2E3024L1T2V9KA"
    HasHardDisk := true
}

; Set chassis vendor to system manufacturer if available
if (SYS_MANUFACTURER != "")
    DmiChassisVendor := SYS_MANUFACTURER

; Set chassis version to system product if available
if (SYS_PRODUCT != "")
    DmiChassisVersion := SYS_PRODUCT

; Set chassis serial to system serial if available
if (SYS_SERIAL != "")
    DmiChassisSerial := SYS_SERIAL

; ============= APPLY VIRTUALBOX ANTI-DETECTION CONFIGURATION =============

; Display configuration summary
MsgBox, 64, Hardware Detection Complete, VirtualBox Anti-Detection Configuration`n`nVM: %VM_NAME%`nHardware: %SYS_MANUFACTURER% %SYS_PRODUCT%`nBIOS: %BIOS_VENDOR% %BIOS_VERSION%`n`nApplying configuration...

; Stop VM if running
RunWait, "%vboxman%" controlvm "%VM_NAME%" poweroff, , Hide

; Wait for VM to stop
Sleep, 2000

; Apply basic VM configuration with host-matching CPU cores
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --memory %SystemMemory%, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --cpus %VMCPUCount%, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --mouse %VM_Mouse%, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --graphicscontroller %VM_VGA%, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --vram %VGA_VRAM%, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --vrde off, , Hide

; Apply advanced anti-detection CPU and timer settings
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/CPUM/EnableHVP" "0", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/TM/TSCMode" "RealTSCOffset", , Hide

; Advanced TSC configuration - make TSC reflect guest execution time for better host matching
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/TM/TSCTiedToExecution" "1", , Hide

; Disable guest time synchronization to prevent time-based detection
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/VMMDev/0/Config/GetHostTimeDisabled" "1", , Hide

; Enable SSE4.1/SSE4.2 instruction passthrough for better CPU matching
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --cpuid-portability-level 0, , Hide

; Configure CPU features to match host more closely and hide VM detection
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/CPUM/IsaExts/SSE4.1" "1", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/CPUM/IsaExts/SSE4.2" "1", , Hide

; Advanced CPU anti-detection - only use documented VirtualBox parameters
; Enable CPU power management features to avoid frequency scaling detection
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/CPUM/PortableCpuIdLevel" "0", , Hide

; Note: MONITOR/MWAIT, EST, ARAT, APIC, HTT parameters are not documented in VirtualBox 7.1
; Only SSE4.1 and SSE4.2 are officially documented as valid IsaExts parameters
; Removed invalid parameters to prevent VirtualBox configuration errors

; Apply BIOS Information (DMI type 0) - following Oracle VirtualBox documentation
; Handle empty strings properly using <EMPTY> as per documentation
BiosVendorValue := (BIOS_VENDOR != "") ? BIOS_VENDOR : "<EMPTY>"
BiosVersionValue := (BIOS_VERSION != "") ? BIOS_VERSION : "<EMPTY>"
BiosDateValue := (BIOS_DATE != "") ? BIOS_DATE : "<EMPTY>"

RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "%BiosVendorValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "%BiosVersionValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "%BiosDateValue%", , Hide
; These are integers as per Oracle documentation
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" %DmiBIOSReleaseMajor%, , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" %DmiBIOSReleaseMinor%, , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" %DmiBIOSFirmwareMajor%, , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" %DmiBIOSFirmwareMinor%, , Hide

; Apply System Information (DMI type 1) - handle empty strings and numeric strings properly
SystemVendorValue := (SYS_MANUFACTURER != "") ? SYS_MANUFACTURER : "<EMPTY>"
SystemProductValue := (SYS_PRODUCT != "") ? SYS_PRODUCT : "<EMPTY>"
SystemVersionValue := (SYS_VERSION != "") ? SYS_VERSION : "<EMPTY>"
; Handle potential numeric serial numbers with string: prefix as per Oracle docs
SystemSerialValue := (SYS_SERIAL != "") ? (RegExMatch(SYS_SERIAL, "^\d+$") ? "string:" . SYS_SERIAL : SYS_SERIAL) : "<EMPTY>"
SystemSKUValue := (SYS_SKU != "") ? SYS_SKU : "<EMPTY>"
SystemFamilyValue := (SYS_FAMILY != "") ? SYS_FAMILY : "<EMPTY>"
SystemUuidValue := (SYS_UUID != "") ? SYS_UUID : "<EMPTY>"

RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "%SystemVendorValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "%SystemProductValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "%SystemVersionValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "%SystemSerialValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "%SystemSKUValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "%SystemFamilyValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "%SystemUuidValue%", , Hide

; Apply Board Information
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "%BOARD_MANUFACTURER%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "%BOARD_PRODUCT%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "%BOARD_VERSION%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "%BOARD_SERIAL%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "%DmiBoardAssetTag%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "%DmiBoardLocInChass%", , Hide
;RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" "%DmiBoardBoardType%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" "10", , Hide

; Apply Chassis Information
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "%DmiChassisVendor%", , Hide
; RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" "%DmiChassisType%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" "3", , Hide

RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "%DmiChassisVersion%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "%DmiChassisSerial%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "%DmiChassisAssetTag%", , Hide

; Apply Processor Information (DMI type 4) - as per Oracle documentation
; Get processor information if not already detected
if (DmiProcManufacturer = "") {
    RunWait, %comspec% /c wmic cpu get Manufacturer /value > %A_Temp%\cpu_mfg.txt, , Hide
    FileRead, cpuMfgInfo, %A_Temp%\cpu_mfg.txt
    if (!ErrorLevel && cpuMfgInfo != "") {
        if (RegExMatch(cpuMfgInfo, "Manufacturer=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified")
                DmiProcManufacturer := temp
        }
    }
}

if (DmiProcVersion = "") {
    RunWait, %comspec% /c wmic cpu get Name /value > %A_Temp%\cpu_name.txt, , Hide
    FileRead, cpuNameInfo, %A_Temp%\cpu_name.txt
    if (!ErrorLevel && cpuNameInfo != "") {
        if (RegExMatch(cpuNameInfo, "Name=(.+)", match)) {
            temp := Trim(match1)
            if (temp != "" && temp != "Not Specified")
                DmiProcVersion := temp
        }
    }
}

; Set default values if still empty
if (DmiProcManufacturer = "")
    DmiProcManufacturer := "GenuineIntel"
if (DmiProcVersion = "")
    DmiProcVersion := "Intel(R) Core(TM) Processor"

ProcManufacturerValue := (DmiProcManufacturer != "") ? DmiProcManufacturer : "<EMPTY>"
ProcVersionValue := (DmiProcVersion != "") ? DmiProcVersion : "<EMPTY>"

RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiProcManufacturer" "%ProcManufacturerValue%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiProcVersion" "%ProcVersionValue%", , Hide

; Apply OEM Information
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "%DmiOEMVBoxVer%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "%DmiOEMVBoxRev%", , Hide

; Apply Enhanced AHCI Storage Configuration with host-matching features
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "%HDD_MANUFACTURER%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "%HDD_FIRMWARE%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "%HDD_Serial%", , Hide

; Detect if host has SSD and configure non-rotational flag accordingly
RunWait, %comspec% /c wmic diskdrive get MediaType /value > %A_Temp%\mediatype.txt, , Hide
FileRead, mediaTypeInfo, %A_Temp%\mediatype.txt
if (!ErrorLevel && InStr(mediaTypeInfo, "SSD")) {
    ; Mark as non-rotational (SSD) to match host
    RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/NonRotational" "1", , Hide
} else {
    ; Mark as rotational (HDD) to match host
    RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/NonRotational" "0", , Hide
}

; Configure DVD/CD drive to match host optical drive
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "%ATAPIVendorId%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "%ATAPIProductId%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "%ATAPIRevision%", , Hide

; Apply Enhanced ACPI Configuration to match host system
; Try to get host ACPI OEM information
RunWait, %comspec% /c wmic computersystem get OEMStringArray /value > %A_Temp%\oemstring.txt, , Hide
FileRead, oemStringInfo, %A_Temp%\oemstring.txt
if (!ErrorLevel && oemStringInfo != "") {
    ; Use host OEM information if available
    if (RegExMatch(oemStringInfo, "OEMStringArray=(.+)", match)) {
        temp := Trim(match1)
        if (temp != "" && StrLen(temp) <= 6) {
            AcpiOemId := temp . Space(6 - StrLen(temp))  ; Pad to 6 characters
        }
    }
}

; Apply ACPI configuration with host-derived or fallback values
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "%AcpiOemId%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/acpi/0/Config/AcpiCreatorId" "%AcpiCreatorId%", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/acpi/0/Config/AcpiCreatorRev" "%AcpiCreatorRev%", , Hide

; Helper function for padding strings
Space(n) {
    spaces := ""
    Loop, %n% {
        spaces .= " "
    }
    return spaces
}

; Apply Hardware Virtualization Settings with VBoxHardenedLoader-inspired anti-detection
; CRITICAL: Paravirtualization MUST be "legacy" to hide hypervisor presence bit
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --paravirtprovider legacy, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --hwvirtex on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --vtxvpid on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --vtxux on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --apic on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --pae on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --longmode on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --hpet on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --nestedpaging on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --largepages on, , Hide

; VBoxHardenedLoader critical settings - Graphics and Input
; CRITICAL: Graphics controller must be VMSVGA, disable 3D/2D acceleration
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --graphicscontroller vmsvga, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --accelerate3d off, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --accelerate2dvideo off, , Hide

; CRITICAL: Pointing device must be PS/2 Mouse (not USB Tablet)
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --mouse ps2, , Hide

; Additional anti-detection hardware settings
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --rtcuseutc on, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --bioslogofadein off, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --bioslogofadeout off, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --bioslogodisplaytime 0, , Hide
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --biosbootmenu disabled, , Hide

; Hide VirtualBox-specific devices and features
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/VMMDev/0/Config/TestingEnabled" "0", , Hide
RunWait, "%vboxman%" setextradata "%VM_NAME%" "VBoxInternal/Devices/VMMDev/0/Config/TestingMMIO" "0", , Hide

; Enhanced MAC address generation - try to match host network adapter vendor
HostMacOUI := "080027"  ; Default VirtualBox OUI
RunWait, %comspec% /c wmic path win32_networkadapter where "NetConnectionStatus=2" get MACAddress /value > %A_Temp%\hostmac.txt, , Hide
FileRead, hostMacInfo, %A_Temp%\hostmac.txt
if (!ErrorLevel && hostMacInfo != "") {
    if (RegExMatch(hostMacInfo, "MACAddress=([0-9A-F]{2}):([0-9A-F]{2}):([0-9A-F]{2})", match)) {
        ; Extract OUI from host MAC address for better matching
        HostMacOUI := match1 . match2 . match3
        ; But use Intel OUI for better compatibility (many detection tools expect Intel)
        if (HostMacOUI != "080027") {
            ; Use a known Intel OUI that's commonly used in enterprise
            HostMacOUI := "001B21"  ; Intel Corporate OUI
        }
    }
}

; Generate random MAC address with host-matching or Intel OUI
Random, mac1, 0, 255
Random, mac2, 0, 255
Random, mac3, 0, 255
hex1 := Format("{:02X}", mac1)
hex2 := Format("{:02X}", mac2)
hex3 := Format("{:02X}", mac3)
macAddress := HostMacOUI . hex1 . hex2 . hex3
RunWait, "%vboxman%" modifyvm "%VM_NAME%" --macaddress1 %macAddress%, , Hide

; Display completion message
MsgBox, 64, Configuration Complete, VirtualBox Anti-Detection Configuration Complete!`n`nVM: %VM_NAME%`nHardware Profile: %SYS_MANUFACTURER% %SYS_PRODUCT%`nBIOS: %BIOS_VENDOR% %BIOS_VERSION% (%BIOS_DATE%)`nBoard: %BOARD_MANUFACTURER% %BOARD_PRODUCT%`nUUID: %SYS_UUID%`nMAC Address: %macAddress%`n`nAll settings have been applied successfully!`n`nYou can now start the VM with:`n"%vboxman%" startvm "%VM_NAME%"

; Save configuration log with debug information and all applied VirtualBox settings
output := "; VirtualBox Anti-Detection Configuration Applied`n"
output .= "; Generated and applied on: " . A_Now . "`n"
output .= "; Target VM: " . VM_NAME . "`n"
output .= "; VirtualBox Path: " . VBoxPath . "`n`n"
output .= "[DEBUG_INFO]`n"
output .= "DMI_FILE_SIZE=" . fileSize . "`n"
output .= "TOTAL_LINES=" . Lines.Length() . "`n"
output .= "SYSTEM_MEMORY_DETECTED=" . SystemMemory . "MB`n"
output .= "TOTAL_PHYSICAL_MEMORY=" . Round(memMatch1 / 1024 / 1024) . "MB`n"
output .= "HOST_CPU_CORES=" . SystemCPUCores . "`n"
output .= "HOST_CPU_THREADS=" . SystemCPUThreads . "`n"
output .= "VM_CPU_COUNT=" . VMCPUCount . "`n`n"
output .= "[HARDWARE_PROFILE]`n"
output .= "BIOS_VENDOR=" . BIOS_VENDOR . "`n"
output .= "BIOS_VERSION=" . BIOS_VERSION . "`n"
output .= "BIOS_DATE=" . BIOS_DATE . "`n"
output .= "SYS_MANUFACTURER=" . SYS_MANUFACTURER . "`n"
output .= "SYS_PRODUCT=" . SYS_PRODUCT . "`n"
output .= "SYS_VERSION=" . SYS_VERSION . "`n"
output .= "SYS_SERIAL=" . SYS_SERIAL . "`n"
output .= "SYS_UUID=" . SYS_UUID . "`n"
output .= "SYS_SKU=" . SYS_SKU . "`n"
output .= "SYS_FAMILY=" . SYS_FAMILY . "`n"
output .= "BOARD_MANUFACTURER=" . BOARD_MANUFACTURER . "`n"
output .= "BOARD_PRODUCT=" . BOARD_PRODUCT . "`n"
output .= "BOARD_VERSION=" . BOARD_VERSION . "`n"
output .= "BOARD_SERIAL=" . BOARD_SERIAL . "`n"
output .= "HDD_MANUFACTURER=" . HDD_MANUFACTURER . "`n"
output .= "HDD_FIRMWARE=" . HDD_FIRMWARE . "`n"
output .= "HDD_Serial=" . HDD_Serial . "`n"
output .= "MAC_ADDRESS=" . macAddress . "`n`n"

output .= "[APPLIED_VBOX_SETTINGS]`n"
output .= "; Basic VM Configuration`n"
output .= "memory=" . SystemMemory . "`n"
output .= "cpus=" . VMCPUCount . "`n"
output .= "mouse=" . VM_Mouse . "`n"
output .= "graphicscontroller=" . VM_VGA . "`n"
output .= "vram=" . VGA_VRAM . "`n"
output .= "vrde=off`n"
output .= "paravirtprovider=legacy`n"
output .= "hwvirtex=on`n"
output .= "vtxvpid=on`n"
output .= "vtxux=on`n"
output .= "apic=on`n"
output .= "pae=on`n"
output .= "longmode=on`n"
output .= "hpet=on`n"
output .= "nestedpaging=on`n"
output .= "largepages=on`n"
output .= "macaddress1=" . macAddress . "`n`n"

output .= "; Enhanced Anti-Detection CPU & Timer Settings`n"
output .= "VBoxInternal/CPUM/EnableHVP=0`n"
output .= "VBoxInternal/TM/TSCMode=RealTSCOffset`n"
output .= "VBoxInternal/TM/TSCTiedToExecution=1 (TSC reflects guest execution)`n"
output .= "VBoxInternal/Devices/VMMDev/0/Config/GetHostTimeDisabled=1 (disable time sync)`n"
output .= "cpuid-portability-level=0 (enable CPU feature passthrough)`n"
output .= "VBoxInternal/CPUM/IsaExts/SSE4.1=1`n"
output .= "VBoxInternal/CPUM/IsaExts/SSE4.2=1`n`n"

output .= "; DMI BIOS Information`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor=" . BIOS_VENDOR . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion=" . BIOS_VERSION . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate=" . BIOS_DATE . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor=" . DmiBIOSReleaseMajor . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor=" . DmiBIOSReleaseMinor . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor=" . DmiBIOSFirmwareMajor . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor=" . DmiBIOSFirmwareMinor . "`n`n"

output .= "; DMI System Information`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor=" . SYS_MANUFACTURER . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct=" . SYS_PRODUCT . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion=" . SYS_VERSION . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial=" . SYS_SERIAL . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU=" . SYS_SKU . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily=" . SYS_FAMILY . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid=" . SYS_UUID . "`n`n"

output .= "; DMI Board Information`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor=" . BOARD_MANUFACTURER . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct=" . BOARD_PRODUCT . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion=" . BOARD_VERSION . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial=" . BOARD_SERIAL . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag=" . DmiBoardAssetTag . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass=" . DmiBoardLocInChass . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType=10`n`n"

output .= "; DMI Chassis Information (type 3)`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor=" . DmiChassisVendor . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType=3 (integer)`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion=" . DmiChassisVersion . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial=" . DmiChassisSerial . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag=" . DmiChassisAssetTag . "`n`n"

output .= "; DMI Processor Information (type 4)`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiProcManufacturer=" . DmiProcManufacturer . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiProcVersion=" . DmiProcVersion . "`n`n"

output .= "; DMI OEM Information (type 11)`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer=" . DmiOEMVBoxVer . "`n"
output .= "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev=" . DmiOEMVBoxRev . "`n`n"

output .= "; AHCI Storage Configuration`n"
output .= "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber=" . HDD_MANUFACTURER . "`n"
output .= "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision=" . HDD_FIRMWARE . "`n"
output .= "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber=" . HDD_Serial . "`n"
output .= "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId=" . ATAPIVendorId . "`n"
output .= "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId=" . ATAPIProductId . "`n"
output .= "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision=" . ATAPIRevision . "`n`n"

output .= "; ACPI Configuration`n"
output .= "VBoxInternal/Devices/acpi/0/Config/AcpiOemId=" . AcpiOemId . "`n"

FileDelete, vm3_config_applied.log
FileAppend, %output%, vm3_config_applied.log

ExitApp
