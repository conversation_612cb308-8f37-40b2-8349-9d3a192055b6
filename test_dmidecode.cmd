@echo off
REM Test script for the new dynamic dmidecode.ahk script

echo ============================================================================
echo Testing Dynamic VirtualBox Anti-Detection Script
echo ============================================================================
echo.

REM Check if VM name is provided
if "%1"=="" (
    echo Usage: %0 ^<VM_NAME^>
    echo Example: %0 "Windows 10"
    echo.
    echo This script will:
    echo 1. Detect current system hardware using dmidecode.exe and WMIC
    echo 2. Apply VirtualBox anti-detection configuration directly to the specified VM
    echo 3. Use actual hardware specifications from this system
    echo.
    exit /b 1
)

set VM_NAME=%1

echo Testing VM: %VM_NAME%
echo.

REM Check if dmidecode.ahk exists
if not exist "dmidecode.ahk" (
    echo Error: dmidecode.ahk not found in current directory
    exit /b 1
)

REM Check if dmidecode.exe exists
if not exist "bin\dmidecode.exe" (
    echo Error: bin\dmidecode.exe not found
    echo Please ensure dmidecode.exe is in the bin\ subdirectory
    exit /b 1
)

REM Check if vm3.ini exists
if not exist "vm3.ini" (
    echo Error: vm3.ini not found
    echo Please ensure vm3.ini is in the current directory
    exit /b 1
)

echo All required files found.
echo.
echo Running dynamic hardware detection and VirtualBox configuration...
echo.

REM Run the AutoHotkey script with the VM name as parameter
"dmidecode.ahk" "%VM_NAME%"

if errorlevel 1 (
    echo.
    echo Error: dmidecode.ahk script failed
    exit /b 1
)

echo.
echo ============================================================================
echo Configuration completed successfully!
echo.
echo Check vm3_config_applied.log for detailed configuration information.
echo.
echo You can now start your VM with:
echo "C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" startvm "%VM_NAME%"
echo ============================================================================
