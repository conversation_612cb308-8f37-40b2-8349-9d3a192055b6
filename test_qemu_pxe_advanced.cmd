@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM QEMU PXE Boot Test Script - Advanced Version
REM Creates a VM with no disk/cdrom that boots from PXE network
REM Includes hardware acceleration and better emulation options
REM ============================================================================

echo ============= QEMU PXE Boot Test - Advanced =============
echo.

REM Configuration variables
set VM_NAME=qemu-pxe-advanced
set VM_MEMORY=4096
set VM_CPUS=4
set QEMU_PATH=qemu-system-x86_64.exe

REM Check if QEMU is available
where %QEMU_PATH% >nul 2>&1
if errorlevel 1 (
    echo Error: QEMU not found in PATH
    echo.
    echo Please install QEMU and add it to your PATH, or specify full path
    echo Download from: https://www.qemu.org/download/
    echo.
    set /p QEMU_PATH="Enter full path to qemu-system-x86_64.exe (or press Enter to exit): "
    if "!QEMU_PATH!"=="" exit /b 1
    
    if not exist "!QEMU_PATH!" (
        echo Error: QEMU executable not found at specified path
        pause
        exit /b 1
    )
)

echo [+] Using QEMU: %QEMU_PATH%
echo.

REM Check for hardware acceleration support
set ACCEL_TYPE=tcg
where haxm >nul 2>&1
if not errorlevel 1 (
    set ACCEL_TYPE=hax
    echo [+] Intel HAXM detected - using hardware acceleration
) else (
    REM Check for WHPX (Windows Hypervisor Platform)
    powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-Hypervisor" | findstr "Enabled" >nul 2>&1
    if not errorlevel 1 (
        set ACCEL_TYPE=whpx
        echo [+] Windows Hypervisor Platform detected - using WHPX acceleration
    ) else (
        echo [+] Using TCG software emulation (slower but compatible)
    )
)

REM Generate random MAC address with Intel OUI (similar to VirtualBox)
set /a "mac1=%random% %% 256"
set /a "mac2=%random% %% 256"
set /a "mac3=%random% %% 256"
call :tohex !mac1! hex1
call :tohex !mac2! hex2  
call :tohex !mac3! hex3
set MAC_ADDRESS=08:00:27:!hex1!:!hex2!:!hex3!

echo [+] Generated MAC address: %MAC_ADDRESS%
echo [+] Acceleration: %ACCEL_TYPE%
echo.

REM Build QEMU command with advanced options
set QEMU_CMD="%QEMU_PATH%"
set QEMU_CMD=%QEMU_CMD% -name "%VM_NAME%"
set QEMU_CMD=%QEMU_CMD% -m %VM_MEMORY%
set QEMU_CMD=%QEMU_CMD% -smp %VM_CPUS%,cores=%VM_CPUS%,threads=1,sockets=1

REM Machine and acceleration
if "%ACCEL_TYPE%"=="hax" (
    set QEMU_CMD=%QEMU_CMD% -machine pc-q35-7.2,accel=hax
) else if "%ACCEL_TYPE%"=="whpx" (
    set QEMU_CMD=%QEMU_CMD% -machine pc-q35-7.2,accel=whpx
) else (
    set QEMU_CMD=%QEMU_CMD% -machine pc-q35-7.2,accel=tcg
)

REM CPU configuration
set QEMU_CMD=%QEMU_CMD% -cpu host
if "%ACCEL_TYPE%"=="tcg" set QEMU_CMD=%QEMU_CMD% -cpu qemu64

REM Boot and timing
set QEMU_CMD=%QEMU_CMD% -rtc base=localtime,clock=host
set QEMU_CMD=%QEMU_CMD% -boot order=n,menu=on,splash-time=3000

REM Network configuration for PXE boot
set QEMU_CMD=%QEMU_CMD% -netdev user,id=net0,bootfile=pxelinux.0,tftp=./tftp
set QEMU_CMD=%QEMU_CMD% -device e1000,netdev=net0,mac=%MAC_ADDRESS%

REM Graphics and display
set QEMU_CMD=%QEMU_CMD% -vga std
set QEMU_CMD=%QEMU_CMD% -display gtk,grab-on-hover=on

REM Audio (optional)
set QEMU_CMD=%QEMU_CMD% -audiodev dsound,id=audio0
set QEMU_CMD=%QEMU_CMD% -device ac97,audiodev=audio0

REM USB support
set QEMU_CMD=%QEMU_CMD% -usb
set QEMU_CMD=%QEMU_CMD% -device usb-tablet

REM Monitoring and control
set QEMU_CMD=%QEMU_CMD% -monitor stdio
set QEMU_CMD=%QEMU_CMD% -no-reboot

REM BIOS/UEFI options
set QEMU_CMD=%QEMU_CMD% -bios bios-256k.bin

echo ============================================================================
echo QEMU Advanced PXE Configuration:
echo VM Name: %VM_NAME%
echo Memory: %VM_MEMORY% MB
echo CPUs: %VM_CPUS%
echo Acceleration: %ACCEL_TYPE%
echo MAC Address: %MAC_ADDRESS%
echo Machine Type: Q35 chipset
echo Network: Intel E1000 with PXE support
echo Boot Order: Network (PXE) only
echo Storage: None (diskless PXE boot)
echo Graphics: Standard VGA with GTK display
echo Audio: AC97 with DirectSound
echo USB: Tablet device for better mouse integration
echo ============================================================================
echo.

echo Creating TFTP directory for PXE files (if needed)...
if not exist "tftp" mkdir tftp
echo [+] TFTP directory ready at: %CD%\tftp
echo.

echo Starting QEMU VM...
echo.
echo Instructions:
echo - The VM will attempt PXE boot from network
echo - If no PXE server is available, you'll see boot failure
echo - Use QEMU monitor commands in this console (type 'help' for commands)
echo - Press Ctrl+Alt+G to release mouse/keyboard from VM
echo - Type 'quit' in monitor to stop VM, or close the VM window
echo.

REM Log the command for debugging
echo Full command:
echo %QEMU_CMD%
echo.

REM Start QEMU
%QEMU_CMD%

echo.
echo VM has stopped.
pause
goto :eof

REM Function to convert decimal to hex
:tohex
set /a "dec=%1"
set "hex="
set "digits=0123456789ABCDEF"
if %dec% equ 0 (
    set "hex=00"
    goto :tohex_end
)
:tohex_loop
if %dec% equ 0 goto :tohex_end
set /a "remainder=dec %% 16"
set /a "dec=dec / 16"
call set "digit=%%digits:~%remainder%,1%%"
set "hex=%digit%%hex%"
goto :tohex_loop
:tohex_end
if "%hex:~1,1%"=="" set "hex=0%hex%"
set "%2=%hex%"
goto :eof

endlocal
