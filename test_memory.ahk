#NoEnv
#SingleInstance Force

; Test memory calculation
RunWait, %comspec% /c wmic computersystem get TotalPhysicalMemory /value > %A_Temp%\memory.txt, , Hide
FileRead, memoryInfo, %A_Temp%\memory.txt
RegExMatch(memoryInfo, "TotalPhysicalMemory=(\d+)", memMatch)

if (memMatch1) {
    ; Convert bytes to MB
    totalMemoryMB := Round(memMatch1 / 1024 / 1024)
    
    ; Assign half of system memory to VM
    halfMemoryMB := Round(totalMemoryMB / 2)
    
    ; Round to nearest common VM memory size, but be more generous for half-memory
    if (halfMemoryMB >= 10240)        ; >= 10GB (for 20GB+ systems)
        SystemMemory := 12288
    else if (halfMemoryMB >= 6144)    ; >= 6GB (for 12GB+ systems)
        SystemMemory := 8192
    else if (halfMemoryMB >= 3584)    ; >= 3.5GB (for 7GB+ systems)
        SystemMemory := 4096
    else if (halfMemoryMB >= 2560)    ; >= 2.5GB (for 5GB+ systems)
        SystemMemory := 3072
    else if (halfMemoryMB >= 1536)    ; >= 1.5GB (for 3GB+ systems)
        SystemMemory := 2048
    else if (halfMemoryMB >= 768)     ; >= 768MB (for 1.5GB+ systems)
        SystemMemory := 1024
    else
        SystemMemory := 512           ; Minimum 512MB
} else {
    SystemMemory := 4096 ; fallback
}

; Create output
output := "Memory Calculation Test:`n"
output .= "Raw Memory Bytes: " . memMatch1 . "`n"
output .= "Total Memory MB: " . totalMemoryMB . "`n"
output .= "Half Memory MB: " . halfMemoryMB . "`n"
output .= "Assigned VM Memory: " . SystemMemory . "`n"

FileDelete, memory_test.log
FileAppend, %output%, memory_test.log

MsgBox, 64, Memory Test, %output%

ExitApp
