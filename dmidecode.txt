# dmidecode 2.10
SMBIOS 2.8 present.
93 structures occupying 4514 bytes.
Table at 0x00743CA0.

Handle 0x0000, DMI type 0, 26 bytes
BIOS Information
	Vendor: American Megatrends Inc.
	Version: F12
	Release Date: 01/16/2019
	Address: 0xF0000
	Runtime Size: 64 kB
	ROM Size: 16384 kB
	Characteristics:
		PCI is supported
		BIOS is upgradeable
		BIOS shadowing is allowed
		Boot from CD is supported
		Selectable boot is supported
		BIOS ROM is socketed
		EDD is supported
		5.25"/1.2 MB floppy services are supported (int 13h)
		3.5"/720 kB floppy services are supported (int 13h)
		3.5"/2.88 MB floppy services are supported (int 13h)
		Print screen service is supported (int 5h)
		Serial services are supported (int 14h)
		Printer services are supported (int 17h)
		ACPI is supported
		USB legacy is supported
		BIOS boot specification is supported
		Targeted content distribution is supported
	BIOS Revision: 5.13

Handle 0x0001, DMI type 1, 27 bytes
System Information
	Manufacturer: Gigabyte Technology Co., Ltd.
	Product Name: H370HD3
	Version: Default string
	Serial Number: Default string
	UUID: 03D502E0-045E-0587-4906-A30700080009
	Wake-up Type: APM Timer
	SKU Number: Default string
	Family: Default string

Handle 0x0002, DMI type 2, 15 bytes
Base Board Information
	Manufacturer: Gigabyte Technology Co., Ltd.
	Product Name: H370 HD3-CF
	Version: x.x
	Serial Number: Default string
	Asset Tag: Default string
	Features:
		Board is a hosting board
		Board is replaceable
	Location In Chassis: Default string
	Chassis Handle: 0x0003
	Type: Motherboard
	Contained Object Handles: 0

Handle 0x0003, DMI type 3, 22 bytes
Chassis Information
	Manufacturer: Default string
	Type: Desktop
	Lock: Not Present
	Version: Default string
	Serial Number: Default string
	Asset Tag: Default string
	Boot-up State: Safe
	Power Supply State: Safe
	Thermal State: Safe
	Security Status: None
	OEM Information: 0x********
	Height: Unspecified
	Number Of Power Cords: 1
	Contained Elements: 0

Handle 0x0004, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J1A1
	Internal Connector Type: None
	External Reference Designator: PS2Mouse
	External Connector Type: PS/2
	Port Type: Mouse Port

Handle 0x0005, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J1A1
	Internal Connector Type: None
	External Reference Designator: Keyboard
	External Connector Type: PS/2
	Port Type: Keyboard Port

Handle 0x0006, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J2A1
	Internal Connector Type: None
	External Reference Designator: TV Out
	External Connector Type: Mini Centronics Type-14
	Port Type: Other

Handle 0x0007, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J2A2A
	Internal Connector Type: None
	External Reference Designator: COM A
	External Connector Type: DB-9 male
	Port Type: Serial Port 16550A Compatible

Handle 0x0008, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J2A2B
	Internal Connector Type: None
	External Reference Designator: Video
	External Connector Type: DB-15 female
	Port Type: Video Port

Handle 0x0009, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J3A1
	Internal Connector Type: None
	External Reference Designator: USB1
	External Connector Type: Access Bus (USB)
	Port Type: USB

Handle 0x000A, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J3A1
	Internal Connector Type: None
	External Reference Designator: USB2
	External Connector Type: Access Bus (USB)
	Port Type: USB

Handle 0x000B, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J3A1
	Internal Connector Type: None
	External Reference Designator: USB3
	External Connector Type: Access Bus (USB)
	Port Type: USB

Handle 0x000C, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9A1 - TPM HDR
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x000D, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9C1 - PCIE DOCKING CONN
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x000E, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J2B3 - CPU FAN
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x000F, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J6C2 - EXT HDMI
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0010, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J3C1 - GMCH FAN
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0011, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J1D1 - ITP
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0012, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9E2 - MDC INTPSR
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0013, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9E4 - MDC INTPSR
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0014, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9E3 - LPC HOT DOCKING
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0015, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9E1 - SCAN MATRIX
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0016, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J9G1 - LPC SIDE BAND
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0017, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J8F1 - UNIFIED
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0018, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J6F1 - LVDS
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x0019, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J2F1 - LAI FAN
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x001A, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J2G1 - GFX VID
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x001B, DMI type 8, 9 bytes
Port Connector Information
	Internal Reference Designator: J1G6 - AC JACK
	Internal Connector Type: Other
	External Reference Designator: Not Specified
	External Connector Type: None
	Port Type: Other

Handle 0x001C, DMI type 9, 17 bytes
System Slot Information
	Designation: J6B2
	Type: x16 PCI Express
	Current Usage: In Use
	Length: Long
	ID: 0
	Characteristics:
		3.3 V is provided
		Opening is shared
		PME signal is supported
	Bus Address: 0000:00:01.0

Handle 0x001D, DMI type 9, 17 bytes
System Slot Information
	Designation: J6B1
	Type: x1 PCI Express
	Current Usage: In Use
	Length: Short
	ID: 1
	Characteristics:
		3.3 V is provided
		Opening is shared
		PME signal is supported
	Bus Address: 0000:00:1c.3

Handle 0x001E, DMI type 9, 17 bytes
System Slot Information
	Designation: J6D1
	Type: x1 PCI Express
	Current Usage: In Use
	Length: Short
	ID: 2
	Characteristics:
		3.3 V is provided
		Opening is shared
		PME signal is supported
	Bus Address: 0000:00:1c.4

Handle 0x001F, DMI type 9, 17 bytes
System Slot Information
	Designation: J7B1
	Type: x1 PCI Express
	Current Usage: In Use
	Length: Short
	ID: 3
	Characteristics:
		3.3 V is provided
		Opening is shared
		PME signal is supported
	Bus Address: 0000:00:1c.5

Handle 0x0020, DMI type 9, 17 bytes
System Slot Information
	Designation: J8B4
	Type: x1 PCI Express
	Current Usage: In Use
	Length: Short
	ID: 4
	Characteristics:
		3.3 V is provided
		Opening is shared
		PME signal is supported
	Bus Address: 0000:00:1c.6

Handle 0x0021, DMI type 10, 6 bytes
On Board Device Information
	Type: Video
	Status: Enabled
	Description:    To Be Filled By O.E.M.

Handle 0x0022, DMI type 11, 5 bytes
OEM Strings
	String 1: Default string

Handle 0x0023, DMI type 12, 5 bytes
System Configuration Options
	Option 1: Default string

Handle 0x0024, DMI type 32, 20 bytes
System Boot Information
	Status: No errors detected

Handle 0x0025, DMI type 34, 11 bytes
Management Device
	Description: LM78-1
	Type: LM78
	Address: 0x********
	Address Type: I/O Port

Handle 0x0026, DMI type 26, 22 bytes
Voltage Probe
	Description: LM78A
	Location: Motherboard
	Status: OK
	Maximum Value: Unknown
	Minimum Value: Unknown
	Resolution: Unknown
	Tolerance: Unknown
	Accuracy: Unknown
	OEM-specific Information: 0x********
	Nominal Value: Unknown

Handle 0x0027, DMI type 36, 16 bytes
Management Device Threshold Data
	Lower Non-critical Threshold: 1
	Upper Non-critical Threshold: 2
	Lower Critical Threshold: 3
	Upper Critical Threshold: 4
	Lower Non-recoverable Threshold: 5
	Upper Non-recoverable Threshold: 6

Handle 0x0028, DMI type 35, 11 bytes
Management Device Component
	Description: Default string
	Management Device Handle: 0x0025
	Component Handle: 0x0026
	Threshold Handle: 0x0027

Handle 0x0029, DMI type 28, 22 bytes
Temperature Probe
	Description: LM78A
	Location: Motherboard
	Status: OK
	Maximum Value: Unknown
	Minimum Value: Unknown
	Resolution: Unknown
	Tolerance: Unknown
	Accuracy: Unknown
	OEM-specific Information: 0x********
	Nominal Value: Unknown

Handle 0x002A, DMI type 36, 16 bytes
Management Device Threshold Data
	Lower Non-critical Threshold: 1
	Upper Non-critical Threshold: 2
	Lower Critical Threshold: 3
	Upper Critical Threshold: 4
	Lower Non-recoverable Threshold: 5
	Upper Non-recoverable Threshold: 6

Handle 0x002B, DMI type 35, 11 bytes
Management Device Component
	Description: Default string
	Management Device Handle: 0x0025
	Component Handle: 0x0029
	Threshold Handle: 0x002A

Handle 0x002C, DMI type 27, 15 bytes
Cooling Device
	Temperature Probe Handle: 0x0029
	Type: Power Supply Fan
	Status: OK
	Cooling Unit Group: 1
	OEM-specific Information: 0x********
	Nominal Speed: Unknown Or Non-rotating

Handle 0x002D, DMI type 36, 16 bytes
Management Device Threshold Data
	Lower Non-critical Threshold: 1
	Upper Non-critical Threshold: 2
	Lower Critical Threshold: 3
	Upper Critical Threshold: 4
	Lower Non-recoverable Threshold: 5
	Upper Non-recoverable Threshold: 6

Handle 0x002E, DMI type 35, 11 bytes
Management Device Component
	Description: Default string
	Management Device Handle: 0x0025
	Component Handle: 0x002C
	Threshold Handle: 0x002D

Handle 0x002F, DMI type 27, 15 bytes
Cooling Device
	Temperature Probe Handle: 0x0029
	Type: Power Supply Fan
	Status: OK
	Cooling Unit Group: 1
	OEM-specific Information: 0x********
	Nominal Speed: Unknown Or Non-rotating

Handle 0x0030, DMI type 36, 16 bytes
Management Device Threshold Data
	Lower Non-critical Threshold: 1
	Upper Non-critical Threshold: 2
	Lower Critical Threshold: 3
	Upper Critical Threshold: 4
	Lower Non-recoverable Threshold: 5
	Upper Non-recoverable Threshold: 6

Handle 0x0031, DMI type 35, 11 bytes
Management Device Component
	Description: Default string
	Management Device Handle: 0x0025
	Component Handle: 0x002F
	Threshold Handle: 0x0030

Handle 0x0032, DMI type 29, 22 bytes
Electrical Current Probe
	Description: ABC
	Location: Motherboard
	Status: OK
	Maximum Value: Unknown
	Minimum Value: Unknown
	Resolution: Unknown
	Tolerance: Unknown
	Accuracy: Unknown
	OEM-specific Information: 0x********
	Nominal Value: Unknown

Handle 0x0033, DMI type 36, 16 bytes
Management Device Threshold Data

Handle 0x0034, DMI type 35, 11 bytes
Management Device Component
	Description: Default string
	Management Device Handle: 0x0025
	Component Handle: 0x0032
	Threshold Handle: 0x0033

Handle 0x0035, DMI type 26, 22 bytes
Voltage Probe
	Description: LM78A
	Location: Power Unit
	Status: OK
	Maximum Value: Unknown
	Minimum Value: Unknown
	Resolution: Unknown
	Tolerance: Unknown
	Accuracy: Unknown
	OEM-specific Information: 0x********
	Nominal Value: Unknown

Handle 0x0036, DMI type 28, 22 bytes
Temperature Probe
	Description: LM78A
	Location: Power Unit
	Status: OK
	Maximum Value: Unknown
	Minimum Value: Unknown
	Resolution: Unknown
	Tolerance: Unknown
	Accuracy: Unknown
	OEM-specific Information: 0x********
	Nominal Value: Unknown

Handle 0x0037, DMI type 27, 15 bytes
Cooling Device
	Temperature Probe Handle: 0x0036
	Type: Power Supply Fan
	Status: OK
	Cooling Unit Group: 1
	OEM-specific Information: 0x********
	Nominal Speed: Unknown Or Non-rotating

Handle 0x0038, DMI type 29, 22 bytes
Electrical Current Probe
	Description: ABC
	Location: Power Unit
	Status: OK
	Maximum Value: Unknown
	Minimum Value: Unknown
	Resolution: Unknown
	Tolerance: Unknown
	Accuracy: Unknown
	OEM-specific Information: 0x********
	Nominal Value: Unknown

Handle 0x0039, DMI type 39, 22 bytes
System Power Supply
	Power Unit Group: 1
	Location: To Be Filled By O.E.M.
	Name: To Be Filled By O.E.M.
	Manufacturer: To Be Filled By O.E.M.
	Serial Number: To Be Filled By O.E.M.
	Asset Tag: To Be Filled By O.E.M.
	Model Part Number: To Be Filled By O.E.M.
	Revision: To Be Filled By O.E.M.
	Max Power Capacity: Unknown
	Status: Present, OK
	Type: Switching
	Input Voltage Range Switching: Auto-switch
	Plugged: Yes
	Hot Replaceable: No
	Input Voltage Probe Handle: 0x0035
	Cooling Device Handle: 0x0037
	Input Current Probe Handle: 0x0038

Handle 0x003A, DMI type 16, 23 bytes
Physical Memory Array
	Location: System Board Or Motherboard
	Use: System Memory
	Error Correction Type: None
	Maximum Capacity: 64 GB
	Error Information Handle: Not Provided
	Number Of Devices: 4

Handle 0x003B, DMI type 17, 40 bytes
Memory Device
	Array Handle: 0x003A
	Error Information Handle: Not Provided
	Total Width: 64 bits
	Data Width: 64 bits
	Size: 8192 MB
	Form Factor: DIMM
	Set: None
	Locator: ChannelA-DIMM0
	Bank Locator: BANK 0
	Type: <OUT OF SPEC>
	Type Detail: Synchronous
	Speed: 2133 MHz
	Manufacturer: 029E
	Serial Number: ********
	Asset Tag: **********
	Part Number: CMK8GX4M1D3000C16   
	Rank: 1

Handle 0x003C, DMI type 17, 40 bytes
Memory Device
	Array Handle: 0x003A
	Error Information Handle: Not Provided
	Total Width: Unknown
	Data Width: Unknown
	Size: No Module Installed
	Form Factor: Unknown
	Set: None
	Locator: ChannelA-DIMM1
	Bank Locator: BANK 1
	Type: Unknown
	Type Detail: None
	Speed: Unknown
	Manufacturer: Not Specified
	Serial Number: Not Specified
	Asset Tag: Not Specified
	Part Number: Not Specified
	Rank: Unknown

Handle 0x003D, DMI type 17, 40 bytes
Memory Device
	Array Handle: 0x003A
	Error Information Handle: Not Provided
	Total Width: Unknown
	Data Width: Unknown
	Size: No Module Installed
	Form Factor: Unknown
	Set: None
	Locator: ChannelB-DIMM0
	Bank Locator: BANK 2
	Type: Unknown
	Type Detail: None
	Speed: Unknown
	Manufacturer: Not Specified
	Serial Number: Not Specified
	Asset Tag: Not Specified
	Part Number: Not Specified
	Rank: Unknown

Handle 0x003E, DMI type 17, 40 bytes
Memory Device
	Array Handle: 0x003A
	Error Information Handle: Not Provided
	Total Width: Unknown
	Data Width: Unknown
	Size: No Module Installed
	Form Factor: Unknown
	Set: None
	Locator: ChannelB-DIMM1
	Bank Locator: BANK 3
	Type: Unknown
	Type Detail: None
	Speed: Unknown
	Manufacturer: Not Specified
	Serial Number: Not Specified
	Asset Tag: Not Specified
	Part Number: Not Specified
	Rank: Unknown

Handle 0x003F, DMI type 19, 31 bytes
Memory Array Mapped Address
	Starting Address: 0x********000
	Ending Address: 0x001FFFFFFFF
	Range Size: 8 GB
	Physical Array Handle: 0x003A
	Partition Width: 0

Handle 0x0040, DMI type 221, 26 bytes
OEM-specific Type
	Header and Data:
		DD 1A 40 00 03 01 00 07 00 31 34 00 02 00 00 00
		00 AA 00 03 00 01 01 00 00 00
	Strings:
		Reference Code - CPU
		uCode Version
		TXT ACM version

Handle 0x0041, DMI type 221, 26 bytes
OEM-specific Type
	Header and Data:
		DD 1A 41 00 03 01 00 07 00 31 34 00 02 00 00 00
		00 00 00 03 04 0C 00 16 1E 05
	Strings:
		Reference Code - ME
		MEBx version
		ME Firmware Version
		Consumer SKU

Handle 0x0042, DMI type 221, 89 bytes
OEM-specific Type
	Header and Data:
		DD 59 42 00 0C 01 00 07 00 31 34 00 02 03 FF FF
		FF FF FF 04 00 FF FF FF 10 00 05 00 FF FF FF 10
		00 06 00 11 00 00 00 00 07 00 02 00 00 00 00 08
		00 09 00 00 00 00 09 00 08 00 00 00 00 0A 00 13
		00 00 00 00 0B 00 07 00 00 00 00 0C 00 06 00 00
		00 00 0D 00 05 00 00 00 00
	Strings:
		Reference Code - CNL PCH
		PCH-CRID Status
		Disabled
		PCH-CRID Original Value
		PCH-CRID New Value
		OPROM - RST - RAID
		CNL PCH H A0 Hsio Version
		CNL PCH H Ax Hsio Version
		CNL PCH H Bx Hsio Version
		CNL PCH LP Ax Hsio Version
		CNL PCH LP B0 Hsio Version
		CNL PCH LP Bx Hsio Version
		CNL PCH LP Dx Hsio Version

Handle 0x0043, DMI type 221, 54 bytes
OEM-specific Type
	Header and Data:
		DD 36 43 00 07 01 00 07 00 31 34 00 02 00 00 07
		01 42 00 03 00 07 00 31 34 00 04 05 FF FF FF FF
		FF 06 00 00 00 00 07 00 07 00 00 00 00 07 00 08
		00 FF FF FF FF FF
	Strings:
		Reference Code - SA - System Agent
		Reference Code - MRC
		SA - PCIe Version
		SA-CRID Status
		Disabled
		SA-CRID Original Value
		SA-CRID New Value
		OPROM - VBIOS

Handle 0x0044, DMI type 7, 19 bytes
Cache Information
	Socket Designation: L1 Cache
	Configuration: Enabled, Not Socketed, Level 1
	Operational Mode: Write Back
	Location: Internal
	Installed Size: 384 kB
	Maximum Size: 384 kB
	Supported SRAM Types:
		Synchronous
	Installed SRAM Type: Synchronous
	Speed: Unknown
	Error Correction Type: Parity
	System Type: Unified
	Associativity: 8-way Set-associative

Handle 0x0045, DMI type 7, 19 bytes
Cache Information
	Socket Designation: L2 Cache
	Configuration: Enabled, Not Socketed, Level 2
	Operational Mode: Write Back
	Location: Internal
	Installed Size: 1536 kB
	Maximum Size: 1536 kB
	Supported SRAM Types:
		Synchronous
	Installed SRAM Type: Synchronous
	Speed: Unknown
	Error Correction Type: Single-bit ECC
	System Type: Unified
	Associativity: 4-way Set-associative

Handle 0x0046, DMI type 7, 19 bytes
Cache Information
	Socket Designation: L3 Cache
	Configuration: Enabled, Not Socketed, Level 3
	Operational Mode: Write Back
	Location: Internal
	Installed Size: 9216 kB
	Maximum Size: 9216 kB
	Supported SRAM Types:
		Synchronous
	Installed SRAM Type: Synchronous
	Speed: Unknown
	Error Correction Type: Multi-bit ECC
	System Type: Unified
	Associativity: <OUT OF SPEC>

Handle 0x0047, DMI type 4, 48 bytes
Processor Information
	Socket Designation: U3E1
	Type: Central Processor
	Family: <OUT OF SPEC>
	Manufacturer: Intel(R) Corporation
	ID: EA 06 09 00 FF FB EB BF
	Version: Intel(R) Core(TM) i5-8600 CPU @ 3.10GHz
	Voltage: 1.0 V
	External Clock: 100 MHz
	Max Speed: 8300 MHz
	Current Speed: 3366 MHz
	Status: Populated, Enabled
	Upgrade: <OUT OF SPEC>
	L1 Cache Handle: 0x0044
	L2 Cache Handle: 0x0045
	L3 Cache Handle: 0x0046
	Serial Number: To Be Filled By O.E.M.
	Asset Tag: To Be Filled By O.E.M.
	Part Number: To Be Filled By O.E.M.
	Core Count: 6
	Core Enabled: 6
	Thread Count: 6
	Characteristics:
		64-bit capable

Handle 0x0048, DMI type 20, 35 bytes
Memory Device Mapped Address
	Starting Address: 0x********000
	Ending Address: 0x001FFFFFFFF
	Range Size: 8 GB
	Physical Device Handle: 0x003B
	Memory Array Mapped Address Handle: 0x003F
	Partition Row Position: Unknown

Handle 0x0049, DMI type 131, 64 bytes
OEM-specific Type
	Header and Data:
		83 40 49 00 35 00 00 00 00 00 00 00 00 00 00 00
		F8 00 04 A3 00 00 00 00 01 00 00 00 00 00 0C 00
		1E 05 16 00 00 00 00 00 FE 00 BC 15 00 00 00 00
		00 00 00 00 26 00 00 00 76 50 72 6F 00 00 00 00

Handle 0x004A, DMI type 13, 22 bytes
BIOS Language Information
	Installable Languages: 15
		en|US|iso8859-1
		zh|TW|unicode
		zh|CN|unicode
		ru|RU|iso8859-5
		de|DE|iso8859-1
		ja|JP|unicode
		ko|KR|unicode
		es|ES|iso8859-1
		fr|FR|iso8859-1
		it|IT|iso8859-1
		pt|PT|iso8859-1
		<BAD INDEX>
		<BAD INDEX>
		<BAD INDEX>
		<BAD INDEX>
	Currently Installed Language: en|US|iso8859-1

Handle 0x004B, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 1
	Bus Address: 0000:00:00.0

Handle 0x004C, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Video
	Type: Video
	Status: Enabled
	Type Instance: 1
	Bus Address: 0000:00:02.0

Handle 0x004D, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 2
	Bus Address: 0000:00:12.0

Handle 0x004E, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 3
	Bus Address: 0000:00:14.0

Handle 0x004F, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 4
	Bus Address: 0000:00:14.2

Handle 0x0050, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 5
	Bus Address: 0000:00:16.0

Handle 0x0051, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 6
	Bus Address: 0000:00:17.0

Handle 0x0052, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 7
	Bus Address: 0000:00:1f.0

Handle 0x0053, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Sound
	Type: Sound
	Status: Enabled
	Type Instance: 1
	Bus Address: 0000:00:1f.3

Handle 0x0054, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 8
	Bus Address: 0000:00:1f.4

Handle 0x0055, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Other
	Type: Other
	Status: Enabled
	Type Instance: 9
	Bus Address: 0000:00:1f.5

Handle 0x0056, DMI type 41, 11 bytes
Onboard Device
	Reference Designation: Onboard - Ethernet
	Type: Ethernet
	Status: Enabled
	Type Instance: 1
	Bus Address: 0000:00:1f.6

Handle 0x0057, DMI type 221, 89 bytes
OEM-specific Type
	Header and Data:
		DD 59 57 00 0C 01 00 00 00 00 A6 00 02 00 FF FF
		FF FF FF 03 04 FF FF FF FF FF 05 06 FF FF FF FF
		FF 07 08 FF FF FF FF FF 09 00 00 00 00 00 00 0A
		00 FF FF FF FF FF 0B 00 FF FF 00 00 00 0C 00 FF
		FF FF FF FF 0D 0E 01 04 01 00 00 0F 00 00 07 00
		00 00 10 00 00 02 00 0D 00
	Strings:
		Lan Phy Version
		Sensor Firmware Version
		Debug Mode Status
		Disabled
		Performance Mode Status
		Disabled
		Debug Use USB(Disabled:Serial)
		Disabled
		ICC Overclocking Version
		UNDI Version
		EC FW Version
		GOP Version
		Royal Park Version
		BP1.4.1.0_RP09
		Platform Version
		Client Silicon Version

Handle 0x0058, DMI type 136, 6 bytes
OEM-specific Type
	Header and Data:
		88 06 58 00 00 00

Handle 0x0059, DMI type 14, 20 bytes
Group Associations
	Name: Firmware Version Info
	Items: 5
		0x0040 (<OUT OF SPEC>)
		0x0041 (<OUT OF SPEC>)
		0x0042 (<OUT OF SPEC>)
		0x0043 (<OUT OF SPEC>)
		0x0057 (<OUT OF SPEC>)

Handle 0x005A, DMI type 14, 8 bytes
Group Associations
	Name: $MEI
	Items: 1
		0x0000 (<OUT OF SPEC>)

Handle 0x005B, DMI type 219, 106 bytes
OEM-specific Type
	Header and Data:
		DB 6A 5B 00 01 04 01 55 02 00 90 06 01 85 30 20
		00 00 00 00 40 00 00 00 00 00 00 00 00 40 00 02
		FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
		FF FF FF FF FF FF FF FF 03 00 00 00 80 00 00 00
		00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
		00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00
		00 00 00 00 00 00 00 00 00 00
	Strings:
		MEI1
		MEI2
		MEI3
		MEI4

Handle 0x005C, DMI type 127, 4 bytes
End Of Table

