#NoEnv
#SingleInstance Force

; Debug memory calculation step by step
halfMemoryMB := 4027

output := "Debug Memory Assignment:`n"
output .= "halfMemoryMB = " . halfMemoryMB . "`n`n"

output .= "Checking conditions:`n"
output .= "halfMemoryMB >= 10240? " . (halfMemoryMB >= 10240) . "`n"
output .= "halfMemoryMB >= 6144? " . (halfMemoryMB >= 6144) . "`n"
output .= "halfMemoryMB >= 3584? " . (halfMemoryMB >= 3584) . "`n"
output .= "halfMemoryMB >= 2560? " . (halfMemoryMB >= 2560) . "`n"

if (halfMemoryMB >= 10240) {
    SystemMemory := 12288
    output .= "`nAssigned: 12288 (condition 1)"
} else if (halfMemoryMB >= 6144) {
    SystemMemory := 8192
    output .= "`nAssigned: 8192 (condition 2)"
} else if (halfMemoryMB >= 3584) {
    SystemMemory := 4096
    output .= "`nAssigned: 4096 (condition 3)"
} else if (halfMemoryMB >= 2560) {
    SystemMemory := 3072
    output .= "`nAssigned: 3072 (condition 4)"
} else {
    SystemMemory := 2048
    output .= "`nAssigned: 2048 (fallback)"
}

output .= "`n`nFinal SystemMemory: " . SystemMemory

FileDelete, debug_memory.log
FileAppend, %output%, debug_memory.log

MsgBox, 64, Debug Memory, %output%

ExitApp
