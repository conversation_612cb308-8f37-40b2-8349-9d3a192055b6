# TAP Driver Setup Guide for QEMU Bridge Networking

This guide explains how to install and configure TAP drivers on Windows for QEMU bridge mode networking.

## Why Bridge Mode?

Bridge mode networking allows your QEMU VM to:
- Appear as a real device on your physical network
- Get IP addresses directly from your router/DHCP server
- Access network services like PXE boot servers
- Communicate directly with other devices on your network

## TAP Driver Installation Options

### Option 1: OpenVPN TAP Driver (Recommended)

**Download and Install:**
1. Go to: https://openvpn.net/community-downloads/
2. Download "OpenVPN for Windows"
3. During installation, ensure "TAP-Windows Adapter V9" is selected
4. Complete the installation

**Alternative - TAP Driver Only:**
1. Go to: https://build.openvpn.net/downloads/releases/
2. Download the latest TAP-Windows installer
3. Install the driver package

### Option 2: TAP-Windows6 Driver

**Download and Install:**
1. Go to: https://github.com/OpenVPN/tap-windows6/releases
2. Download the latest release (e.g., `tap-windows-9.24.7-I601-Win10.exe`)
3. Run the installer as Administrator
4. Follow the installation wizard

## Post-Installation Configuration

### 1. Verify TAP Interface Installation

Open Command Prompt as Administrator and run:
```cmd
netsh interface show interface
```

Look for interfaces with "TAP" in the name, such as:
- "TAP-Windows Adapter V9"
- "Ethernet 2" (TAP interface)

### 2. Identify Your TAP Interface

List all network interfaces:
```cmd
netsh interface show interface
```

Note the exact name of your TAP interface (e.g., "Ethernet 2", "TAP-Windows Adapter V9")

### 3. Configure Bridge (Optional but Recommended)

To bridge your TAP interface with your physical network adapter:

1. Open "Network and Sharing Center"
2. Click "Change adapter settings"
3. Select both your physical network adapter AND the TAP adapter
4. Right-click and select "Bridge Connections"
5. Windows will create a "Network Bridge"

### 4. Update QEMU Scripts

Edit the TAP interface name in your scripts:

**For `test_qemu_pxe_bridge.cmd`:**
```cmd
set TAP_INTERFACE=Ethernet 2
```

**For `qemu_pxe_config.ini`:**
```ini
tap_interface=Ethernet 2
```

Replace "Ethernet 2" with your actual TAP interface name.

## Testing TAP Interface

### Basic Connectivity Test

1. Run the bridge mode script: `test_qemu_pxe_bridge.cmd`
2. The script will check for TAP interfaces
3. If successful, QEMU will start with bridge networking

### Network Verification

In the QEMU VM:
1. Check if the VM gets an IP from your router
2. Verify network connectivity
3. Test PXE boot functionality

## Troubleshooting

### Common Issues

**1. "No TAP interfaces found"**
- Reinstall TAP driver
- Check Windows Device Manager for TAP devices
- Run Command Prompt as Administrator

**2. "Access denied" or permission errors**
- Run QEMU script as Administrator
- Check TAP interface permissions
- Verify bridge configuration

**3. VM doesn't get network connectivity**
- Check bridge configuration
- Verify physical network adapter is working
- Test with user mode networking first

**4. PXE boot doesn't work**
- Ensure PXE server is available on your network
- Check DHCP server configuration
- Verify network bridge is properly configured

### Advanced Troubleshooting

**Check TAP Interface Status:**
```cmd
netsh interface show interface name="TAP-Windows Adapter V9"
```

**Reset Network Configuration:**
```cmd
netsh int ip reset
netsh winsock reset
```

**View Network Bridges:**
```cmd
netsh bridge show adapter
```

## Security Considerations

### Bridge Mode Security
- VM appears as real device on network
- Same security policies apply as physical devices
- Consider network segmentation
- Monitor network traffic

### TAP Interface Permissions
- TAP interfaces require Administrator privileges
- Consider running QEMU as service for production use
- Implement proper access controls

## Alternative: User Mode Networking

If bridge mode is too complex, you can use user mode networking:

1. Edit `qemu_pxe_config.ini`:
   ```ini
   network_mode=user
   ```

2. Use any of the non-bridge scripts:
   - `test_qemu_pxe_stealth.cmd`
   - `test_qemu_pxe_configurable.cmd`

User mode provides NAT networking but may not work with all PXE scenarios.

## Script Usage

### Bridge Mode Scripts
- `test_qemu_pxe_bridge.cmd` - Dedicated bridge mode script
- `test_qemu_pxe_configurable.cmd` - Configurable (set network_mode=bridge)

### Configuration
- Edit `qemu_pxe_config.ini` for persistent settings
- Set `network_mode=bridge` and `tap_interface=YourTAPName`

### Running
1. Install TAP driver
2. Configure bridge (optional)
3. Update interface name in scripts
4. Run as Administrator
5. Start QEMU with bridge networking

## Support

If you encounter issues:
1. Check Windows Event Viewer for TAP-related errors
2. Verify TAP driver installation in Device Manager
3. Test with simple ping/connectivity first
4. Consult QEMU and OpenVPN documentation

The bridge mode provides the most realistic network environment for PXE boot testing and VM anti-detection scenarios.
