# QEMU PXE Boot with Anti-Detection Features

This collection of batch files creates QEMU virtual machines that boot from PXE (network boot) with comprehensive anti-detection features to hide VM signatures from the guest operating system.

## Files Overview

### 1. `test_qemu_pxe.cmd` - Basic Anti-Detection
- Simple QEMU VM with basic anti-detection
- SMBIOS spoofing for BIOS, system, and board information
- CPU feature masking
- No USB or audio devices

### 2. `test_qemu_pxe_stealth.cmd` - Maximum Stealth
- Comprehensive anti-detection implementation
- Full SMBIOS spoofing (Types 0, 1, 2, 3, 4)
- CPU model spoofing (Intel i7-9700K)
- Realistic hardware simulation
- Random serial number generation

### 3. `test_qemu_pxe_configurable.cmd` - Configurable Version
- Reads settings from `qemu_pxe_config.ini`
- Customizable anti-detection features
- Easy to modify without editing batch files

### 4. `qemu_pxe_config.ini` - Configuration File
- Comprehensive configuration options
- Anti-detection settings section
- Hardware spoofing parameters

## Anti-Detection Techniques Implemented

Based on research from:
- [zhaodice/qemu-anti-detection](https://github.com/zhaodice/qemu-anti-detection)
- [A1exxander/KVM-Spoofing](https://github.com/A1exxander/KVM-Spoofing)

### SMBIOS Spoofing
- **Type 0 (BIOS)**: American Megatrends Inc. BIOS with realistic version/date
- **Type 1 (System)**: ASUS PRIME Z390-A motherboard simulation
- **Type 2 (Baseboard)**: Matching motherboard information
- **Type 3 (Chassis)**: Generic chassis information
- **Type 4 (Processor)**: Intel i7-9700K CPU simulation

### CPU Features
- Family: 6, Model: 158, Stepping: 10 (Intel Coffee Lake)
- Model ID: "Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz"
- VMware CPUID frequency reporting disabled
- Hypervisor bit disabled

### Network Configuration
- Intel E1000 network adapter (realistic hardware)
- MAC addresses with Intel OUI (08:00:27:xx:xx:xx)
- PXE boot support with TFTP server simulation

### Disabled VM Features
- No USB devices (common VM detection vector)
- No audio devices (reduces VM fingerprint)
- HPET timer disabled
- ACPI disabled
- Boot menu and splash screen disabled

### Hardware Simulation
- Q35 chipset (modern, realistic)
- Realistic memory and CPU configurations
- Random but realistic serial numbers
- Standard VGA graphics (avoids VM-specific drivers)

## Usage Instructions

### Quick Start (Maximum Stealth)
```cmd
test_qemu_pxe_stealth.cmd
```

### Configurable Setup
1. Edit `qemu_pxe_config.ini` to customize settings
2. Run `test_qemu_pxe_configurable.cmd`

### Requirements
- QEMU installed and in PATH
- Windows with PowerShell (for hardware acceleration detection)
- Network access for PXE boot (or local TFTP server)

## Configuration Options

### Basic Settings
- VM name, memory, CPU count
- Machine type and acceleration
- Network adapter type
- Graphics and display options

### Anti-Detection Settings
- SMBIOS spoofing enable/disable
- Custom BIOS information
- System manufacturer/model
- CPU identification strings
- Serial number generation

### Network Settings
- TFTP directory location
- PXE boot file name
- MAC address prefix
- Network mode (user/bridge)

## Security Considerations

### What This Hides
- QEMU/KVM signatures in SMBIOS tables
- VM-specific hardware identifiers
- Hypervisor CPUID bits
- Common VM device signatures

### What This Doesn't Hide
- Timing-based detection (RDTSC)
- Advanced CPU feature detection
- Memory layout analysis
- Network timing patterns
- Some WMI queries (Win32_Fan, etc.)

### Limitations
- Software emulation (TCG) is slower than hardware acceleration
- Some anti-cheat systems use advanced detection methods
- Perfect stealth requires kernel-level modifications (not included)

## Troubleshooting

### Common Issues
1. **QEMU not found**: Install QEMU and add to PATH
2. **No PXE response**: Set up local TFTP server or use network PXE
3. **Slow performance**: Consider hardware acceleration (HAXM/WHPX)
4. **Detection still occurs**: Some software uses advanced techniques

### Performance Tips
- Use hardware acceleration when possible
- Allocate appropriate memory (not too much/little)
- Use realistic CPU count matching host
- Disable unnecessary features

## Advanced Usage

### Custom SMBIOS Values
Edit the configuration file to match your target hardware:
- Use real manufacturer names
- Match CPU models to memory/performance
- Generate realistic serial numbers
- Use appropriate dates and versions

### Network Boot Setup
1. Create TFTP directory: `mkdir tftp`
2. Place PXE boot files in TFTP directory
3. Configure DHCP/PXE server if needed
4. Test with network boot images

### Integration with Existing Tools
- Can be combined with GPU passthrough
- Works with libvirt/virt-manager XML configs
- Compatible with existing QEMU installations

## Legal Notice

These tools are provided for educational and legitimate testing purposes only. Users are responsible for complying with all applicable laws and terms of service. The authors do not condone or support any illegal activities.

## References

- [QEMU Documentation](https://www.qemu.org/docs/master/)
- [SMBIOS Specification](https://www.dmtf.org/standards/smbios)
- [Intel CPU Identification](https://software.intel.com/content/www/us/en/develop/articles/intel-architecture-and-processor-identification-with-cpuid-model-and-family-numbers.html)
