@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM QEMU PXE Boot Test Script - Configurable Anti-Detection Version
REM Reads configuration from qemu_pxe_config.ini
REM Creates a VM with no disk/cdrom that boots from PXE network
REM Includes comprehensive anti-detection features
REM ============================================================================

set CONFIG_FILE=qemu_pxe_config.ini

echo ============= QEMU PXE Boot Test - Configurable Anti-Detection =============
echo.

REM Check if config file exists
if not exist "%CONFIG_FILE%" (
    echo Error: Configuration file %CONFIG_FILE% not found
    echo Please create the configuration file or use the basic version
    pause
    exit /b 1
)

echo [+] Loading configuration from %CONFIG_FILE%
echo.

REM Read configuration values using a simple parser
call :read_config "%CONFIG_FILE%" "qemu" "vm_name" VM_NAME "qemu-pxe-test"
call :read_config "%CONFIG_FILE%" "qemu" "vm_memory" VM_MEMORY "2048"
call :read_config "%CONFIG_FILE%" "qemu" "vm_cpus" VM_CPUS "2"
call :read_config "%CONFIG_FILE%" "qemu" "qemu_path" QEMU_PATH ""
call :read_config "%CONFIG_FILE%" "qemu" "machine_type" MACHINE_TYPE "pc-q35-7.2"
call :read_config "%CONFIG_FILE%" "qemu" "cpu_type" CPU_TYPE "qemu64"
call :read_config "%CONFIG_FILE%" "qemu" "acceleration" ACCELERATION "auto"
call :read_config "%CONFIG_FILE%" "qemu" "network_adapter" NETWORK_ADAPTER "e1000"
call :read_config "%CONFIG_FILE%" "qemu" "graphics" GRAPHICS "std"
call :read_config "%CONFIG_FILE%" "qemu" "display" DISPLAY "gtk"
call :read_config "%CONFIG_FILE%" "qemu" "audio" AUDIO "dsound"
call :read_config "%CONFIG_FILE%" "qemu" "mac_prefix" MAC_PREFIX "08:00:27"
call :read_config "%CONFIG_FILE%" "qemu" "tftp_dir" TFTP_DIR "./tftp"
call :read_config "%CONFIG_FILE%" "qemu" "bootfile" BOOTFILE "pxelinux.0"

REM Read anti-detection configuration
call :read_config "%CONFIG_FILE%" "anti_detection" "smbios_spoofing" SMBIOS_SPOOFING "yes"
call :read_config "%CONFIG_FILE%" "anti_detection" "bios_vendor" BIOS_VENDOR "American Megatrends Inc."
call :read_config "%CONFIG_FILE%" "anti_detection" "bios_version" BIOS_VERSION "2603"
call :read_config "%CONFIG_FILE%" "anti_detection" "bios_date" BIOS_DATE "04/09/2021"
call :read_config "%CONFIG_FILE%" "anti_detection" "system_manufacturer" SYS_MANUFACTURER "ASUSTeK COMPUTER INC."
call :read_config "%CONFIG_FILE%" "anti_detection" "system_product" SYS_PRODUCT "PRIME Z390-A"
call :read_config "%CONFIG_FILE%" "anti_detection" "system_version" SYS_VERSION "Rev 1.xx"
call :read_config "%CONFIG_FILE%" "anti_detection" "cpu_model_id" CPU_MODEL_ID "Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz"

REM Set QEMU executable
if "%QEMU_PATH%"=="" set QEMU_PATH=qemu-system-x86_64.exe

REM Check if QEMU is available
where "%QEMU_PATH%" >nul 2>&1
if errorlevel 1 (
    if not exist "%QEMU_PATH%" (
        echo Error: QEMU not found at: %QEMU_PATH%
        echo Please install QEMU or update the qemu_path in %CONFIG_FILE%
        pause
        exit /b 1
    )
)

echo [+] Using QEMU: %QEMU_PATH%

REM Detect acceleration if set to auto
if /i "%ACCELERATION%"=="auto" (
    set ACCEL_TYPE=tcg
    where haxm >nul 2>&1
    if not errorlevel 1 (
        set ACCEL_TYPE=hax
        echo [+] Intel HAXM detected - using hardware acceleration
    ) else (
        powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-Hypervisor" 2>nul | findstr "Enabled" >nul 2>&1
        if not errorlevel 1 (
            set ACCEL_TYPE=whpx
            echo [+] Windows Hypervisor Platform detected - using WHPX acceleration
        ) else (
            echo [+] Using TCG software emulation
        )
    )
) else (
    set ACCEL_TYPE=%ACCELERATION%
    echo [+] Using configured acceleration: %ACCEL_TYPE%
)

REM Generate MAC address with Intel OUI for realism
if "%MAC_PREFIX%"=="" set MAC_PREFIX=08:00:27
set /a "mac1=%random% %% 256"
set /a "mac2=%random% %% 256"
set /a "mac3=%random% %% 256"
call :tohex !mac1! hex1
call :tohex !mac2! hex2
call :tohex !mac3! hex3
set MAC_ADDRESS=%MAC_PREFIX%:!hex1!:!hex2!:!hex3!

REM Generate realistic serial numbers
set /a "serial1=%random% %% 10000000"
set /a "serial2=%random% %% 10000000"
set SYSTEM_SERIAL=SYS!serial1!
set BOARD_SERIAL=BSN!serial2!

echo [+] Generated MAC address: %MAC_ADDRESS%
echo [+] Generated System Serial: %SYSTEM_SERIAL%
echo [+] Generated Board Serial: %BOARD_SERIAL%
echo.

REM Build anti-detection QEMU command
set QEMU_CMD="%QEMU_PATH%"
set QEMU_CMD=%QEMU_CMD% -name "%VM_NAME%"
set QEMU_CMD=%QEMU_CMD% -m %VM_MEMORY%
set QEMU_CMD=%QEMU_CMD% -smp %VM_CPUS%,cores=%VM_CPUS%,threads=1,sockets=1
set QEMU_CMD=%QEMU_CMD% -machine %MACHINE_TYPE%,accel=%ACCEL_TYPE%,kernel_irqchip=on

REM CPU with anti-detection features
set QEMU_CMD=%QEMU_CMD% -cpu %CPU_TYPE%,family=6,model=158,stepping=10
set QEMU_CMD=%QEMU_CMD% -cpu %CPU_TYPE%,model_id="%CPU_MODEL_ID%"
set QEMU_CMD=%QEMU_CMD% -cpu %CPU_TYPE%,vmware-cpuid-freq=off,hypervisor=off

REM SMBIOS spoofing if enabled
if /i "%SMBIOS_SPOOFING%"=="yes" (
    set QEMU_CMD=%QEMU_CMD% -smbios type=0,vendor="%BIOS_VENDOR%",version="%BIOS_VERSION%",date="%BIOS_DATE%"
    set QEMU_CMD=%QEMU_CMD% -smbios type=1,manufacturer="%SYS_MANUFACTURER%",product="%SYS_PRODUCT%",version="%SYS_VERSION%",serial="%SYSTEM_SERIAL%"
    set QEMU_CMD=%QEMU_CMD% -smbios type=2,manufacturer="%SYS_MANUFACTURER%",product="%SYS_PRODUCT%",version="%SYS_VERSION%",serial="%BOARD_SERIAL%"
    set QEMU_CMD=%QEMU_CMD% -smbios type=3,manufacturer="Chassis Manufacture",version="Chassis Version",serial="Chassis Serial Number"
    set QEMU_CMD=%QEMU_CMD% -smbios type=4,manufacturer="Intel",version="%CPU_MODEL_ID%",max-speed=3600,current-speed=3600
)

set QEMU_CMD=%QEMU_CMD% -rtc base=localtime,clock=host
set QEMU_CMD=%QEMU_CMD% -boot order=n,menu=off,splash-time=0
set QEMU_CMD=%QEMU_CMD% -netdev user,id=net0,bootfile=%BOOTFILE%,tftp=%TFTP_DIR%
set QEMU_CMD=%QEMU_CMD% -device %NETWORK_ADAPTER%,netdev=net0,mac=%MAC_ADDRESS%
set QEMU_CMD=%QEMU_CMD% -vga %GRAPHICS%
set QEMU_CMD=%QEMU_CMD% -display %DISPLAY%

REM No USB or audio for stealth mode
REM Audio and USB disabled for anti-detection

REM Anti-detection flags
set QEMU_CMD=%QEMU_CMD% -no-hpet
set QEMU_CMD=%QEMU_CMD% -no-acpi
set QEMU_CMD=%QEMU_CMD% -monitor stdio
set QEMU_CMD=%QEMU_CMD% -no-reboot

echo ============================================================================
echo QEMU Configuration Summary:
echo VM Name: %VM_NAME%
echo Memory: %VM_MEMORY% MB
echo CPUs: %VM_CPUS%
echo Machine: %MACHINE_TYPE%
echo CPU: %CPU_TYPE%
echo Acceleration: %ACCEL_TYPE%
echo Network: %NETWORK_ADAPTER% (%MAC_ADDRESS%)
echo Graphics: %GRAPHICS% with %DISPLAY% display
echo Audio: %AUDIO%
echo TFTP Directory: %TFTP_DIR%
echo Boot File: %BOOTFILE%
echo ============================================================================
echo.

REM Create TFTP directory
if not exist "%TFTP_DIR%" (
    mkdir "%TFTP_DIR%" 2>nul
    echo [+] Created TFTP directory: %TFTP_DIR%
) else (
    echo [+] Using existing TFTP directory: %TFTP_DIR%
)
echo.

echo Starting QEMU VM...
echo Type 'quit' in this console to stop the VM
echo.

REM Start QEMU
%QEMU_CMD%

echo.
echo VM has stopped.
pause
goto :eof

REM Function to read INI file values
:read_config
set "file=%~1"
set "section=%~2"
set "key=%~3"
set "var=%~4"
set "default=%~5"

set "value="
set "in_section="

for /f "usebackq delims=" %%a in ("%file%") do (
    set "line=%%a"
    
    REM Check for section header
    if "!line:~0,1!"=="[" (
        set "in_section="
        if "!line!"=="[%section%]" set "in_section=1"
    ) else if defined in_section (
        REM Check for key=value pair
        for /f "tokens=1,2 delims==" %%b in ("!line!") do (
            if "%%b"=="%key%" (
                set "value=%%c"
                goto :read_config_found
            )
        )
    )
)

:read_config_found
if not defined value set "value=%default%"
set "%var%=%value%"
goto :eof

REM Function to convert decimal to hex
:tohex
set /a "dec=%1"
set "hex="
set "digits=0123456789ABCDEF"
if %dec% equ 0 (
    set "hex=00"
    goto :tohex_end
)
:tohex_loop
if %dec% equ 0 goto :tohex_end
set /a "remainder=dec %% 16"
set /a "dec=dec / 16"
call set "digit=%%digits:~%remainder%,1%%"
set "hex=%digit%%hex%"
goto :tohex_loop
:tohex_end
if "%hex:~1,1%"=="" set "hex=0%hex%"
set "%2=%hex%"
goto :eof

endlocal
