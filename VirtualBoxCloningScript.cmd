@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM VirtualBox Anti-Detection Script
REM Based on VBoxHardenedLoader and VBoxAntiDetection GitHub repositories
REM Uses ONLY verified settings from those sources
REM ============================================================================

set VM_NAME=%1

if "%VM_NAME%"=="" (
    echo Usage: %0 ^<VM_NAME^>
    echo Example: %0 "Windows 10"
    exit /b 1
)

REM Hardware info extracted from your dmidecode.txt
set "BIOS_VENDOR=American Megatrends Inc."
set "BIOS_VERSION=F12"
set "BIOS_DATE=01/16/2019"
set "SYS_MANUFACTURER=Gigabyte Technology Co., Ltd."
set "SYS_PRODUCT=H370HD3"
set "SYS_VERSION=Default string"
set "SYS_SERIAL=Default string"
set "SYS_UUID=03D502E0-045E-0587-4906-A30700080009"
set "SYS_SKU=Default string"
set "SYS_FAMILY=Default string"
set "BOARD_MANUFACTURER=Gigabyte Technology Co., Ltd."
set "BOARD_PRODUCT=H370 HD3-CF"
set "BOARD_VERSION=x.x"
set "BOARD_SERIAL=Default string"

set "HDD_MANUFACTURER=Hitachi HTS543230AAA384"
set "HDD_FIRMWARE=ES2OA60W"
set "HDD_Serial=2E3024L1T2V9KA"

echo ============================================================================
echo VirtualBox Anti-Detection Configuration
echo VM: %VM_NAME%
echo Hardware: !SYS_MANUFACTURER! !SYS_PRODUCT!
echo ============================================================================
echo.

REM Stop VM if running
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" controlvm "%VM_NAME%" poweroff >nul 2>&1
timeout /t 2 >nul

echo [+] Applying basic VM configuration...

REM Basic VM configuration (from VBoxAntiDetection)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --memory 4096
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --mouse ps2
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --graphicscontroller vboxsvga
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --vram 128
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --vrde off

echo [+] Applying anti-detection CPU settings...

REM Anti-detection CPU settings (from both repositories)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/CPUM/EnableHVP" "0"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/TM/TSCMode" "RealTSCOffset"

echo [+] Configuring DMI/SMBIOS BIOS information...

REM BIOS Information (adapted with your hardware)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVendor" "!BIOS_VENDOR!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSVersion" "!BIOS_VERSION!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseDate" "!BIOS_DATE!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMajor" "5"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSReleaseMinor" "13"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMajor" "1"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBIOSFirmwareMinor" "0"

echo [+] Configuring DMI/SMBIOS system information...

REM System Information (adapted with your hardware)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVendor" "!SYS_MANUFACTURER!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemProduct" "!SYS_PRODUCT!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemVersion" "!SYS_VERSION!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSerial" "!SYS_SERIAL!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemSKU" "!SYS_SKU!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemFamily" "!SYS_FAMILY!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiSystemUuid" "!SYS_UUID!"

echo [+] Configuring DMI/SMBIOS board information...

REM Board Information (adapted with your hardware)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVendor" "!BOARD_MANUFACTURER!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardProduct" "!BOARD_PRODUCT!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardVersion" "!BOARD_VERSION!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardSerial" "!BOARD_SERIAL!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardAssetTag" "Base Board Asset Tag#"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardLocInChass" "Board Loc In"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiBoardBoardType" "10"

echo [+] Configuring DMI/SMBIOS chassis information...

REM Chassis Information (adapted with your hardware)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVendor" "!SYS_MANUFACTURER!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisType" "3"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisVersion" "!SYS_PRODUCT!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisSerial" "!SYS_SERIAL!"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiChassisAssetTag" "Desktop"

echo [+] Configuring OEM information...

REM OEM Information (from VBoxAntiDetection)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxVer" "Extended version info: 1.00.00"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/pcbios/0/Config/DmiOEMVBoxRev" "Extended revision info: 1A"

echo [+] Configuring AHCI storage devices...

REM AHCI Storage Configuration (from VBoxHardenedLoader)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/ModelNumber" "Hitachi HTS543230AAA384"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/FirmwareRevision" "ES2OA60W"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port0/SerialNumber" "2E3024L1T2V9KA"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIVendorId" "Slimtype"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIProductId" "DVD A  DS8A8SH"
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/ahci/0/Config/Port1/ATAPIRevision" "KAA2"

echo [+] Configuring ACPI settings...

REM ACPI Configuration (from VBoxAntiDetection)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" setextradata "%VM_NAME%" "VBoxInternal/Devices/acpi/0/Config/AcpiOemId" "GBT   "

echo [+] Configuring hardware virtualization settings...

REM Hardware Virtualization Settings (from VBoxAntiDetection)
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --paravirtprovider legacy
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --hwvirtex on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --vtxvpid on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --vtxux on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --apic on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --pae on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --longmode on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --hpet on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --nestedpaging on
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --largepages on

echo [+] Generating random MAC address...

REM Generate random MAC address with Intel OUI (08:00:27)
set /a "mac1=%random% %% 256"
set /a "mac2=%random% %% 256"
set /a "mac3=%random% %% 256"
call :tohex !mac1! hex1
call :tohex !mac2! hex2  
call :tohex !mac3! hex3
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" modifyvm "%VM_NAME%" --macaddress1 "080027!hex1!!hex2!!hex3!"

echo [+] Configuration completed successfully!
echo.
echo ============================================================================
echo CONFIGURATION SUMMARY
echo ============================================================================
echo VM Name: %VM_NAME%
echo Hardware Profile: !SYS_MANUFACTURER! !SYS_PRODUCT!
echo BIOS: !BIOS_VENDOR! !BIOS_VERSION! (!BIOS_DATE!)
echo Board: !BOARD_MANUFACTURER! !BOARD_PRODUCT!
echo UUID: !SYS_UUID!
echo.
echo APPLIED SETTINGS:
echo [✓] DMI/SMBIOS hardware information
echo [✓] Anti-detection CPU settings  
echo [✓] AHCI storage device spoofing
echo [✓] ACPI OEM information
echo [✓] Hardware virtualization optimization
echo [✓] Random MAC address generation
echo.
echo VM is ready to start: "C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" startvm "%VM_NAME%"
echo ============================================================================

goto :eof

:tohex
set /a "dec=%1"
set "hex="
set "digits=0123456789ABCDEF"
if %dec% equ 0 (
    set "hex=00"
    goto :tohex_end
)
:tohex_loop
if %dec% equ 0 goto :tohex_end
set /a "remainder=dec %% 16"
set /a "dec=dec / 16"
call set "digit=%%digits:~%remainder%,1%%"
set "hex=%digit%%hex%"
goto :tohex_loop
:tohex_end
if "%hex:~1,1%"=="" set "hex=0%hex%"
set "%2=%hex%"
goto :eof

endlocal