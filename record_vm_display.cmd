@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM VM Display Recording Helper Script
REM Records QEMU VM display using FFmpeg via VNC connection
REM Requires FFmpeg to be installed and in PATH
REM ============================================================================

echo ============= VM Display Recording Helper =============
echo.
echo This script records QEMU VM display via VNC connection
echo Prerequisites:
echo - QEMU VM must be running with VNC enabled (port 5901)
echo - FFmpeg must be installed and in PATH
echo.

REM Configuration
set VNC_HOST=localhost
set VNC_PORT=5901
set OUTPUT_DIR=recordings
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set OUTPUT_FILE=%OUTPUT_DIR%\vm_recording_%TIMESTAMP%.mp4

REM Check if FFmpeg is available
where ffmpeg >nul 2>&1
if errorlevel 1 (
    echo Error: FFmpeg not found in PATH
    echo.
    echo Please install FFmpeg:
    echo 1. Download from: https://ffmpeg.org/download.html
    echo 2. Extract to a folder (e.g., C:\ffmpeg)
    echo 3. Add C:\ffmpeg\bin to your PATH environment variable
    echo.
    echo Alternative - Use Chocolatey:
    echo   choco install ffmpeg
    echo.
    echo Alternative - Use Winget:
    echo   winget install FFmpeg
    echo.
    pause
    exit /b 1
)

echo [+] FFmpeg found: 
ffmpeg -version | findstr "ffmpeg version"
echo.

REM Create output directory
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
    echo [+] Created recordings directory: %OUTPUT_DIR%
) else (
    echo [+] Using existing recordings directory: %OUTPUT_DIR%
)
echo.

REM Check if VNC server is accessible
echo [+] Checking VNC connection to %VNC_HOST%:%VNC_PORT%...
netstat -an | findstr ":%VNC_PORT%" >nul
if errorlevel 1 (
    echo.
    echo WARNING: No service found on port %VNC_PORT%
    echo.
    echo Make sure your QEMU VM is running with VNC enabled:
    echo   -vnc :%VNC_PORT%,to=99
    echo.
    echo Or start the bridge script with recording enabled.
    echo.
    set /p CONTINUE="Continue anyway? (y/N): "
    if /i not "!CONTINUE!"=="y" exit /b 1
) else (
    echo [+] VNC service detected on port %VNC_PORT%
)
echo.

REM Recording parameters
set FRAMERATE=30
set QUALITY=medium
set CODEC=libx264

echo ============================================================================
echo Recording Configuration:
echo VNC Source: %VNC_HOST%:%VNC_PORT%
echo Output File: %OUTPUT_FILE%
echo Frame Rate: %FRAMERATE% fps
echo Quality: %QUALITY%
echo Codec: %CODEC%
echo ============================================================================
echo.

echo Starting VM display recording...
echo.
echo Instructions:
echo - Recording will start immediately
echo - Press Ctrl+C to stop recording
echo - Output file will be saved to: %OUTPUT_FILE%
echo - File size depends on recording length and activity
echo.

set /p START="Press Enter to start recording (or Ctrl+C to cancel)..."

REM FFmpeg recording command
echo [+] Starting FFmpeg recording...
echo.

ffmpeg -f gdigrab -framerate %FRAMERATE% -i desktop -f vnc -i vnc://%VNC_HOST%:%VNC_PORT% -c:v %CODEC% -preset %QUALITY% -crf 23 -pix_fmt yuv420p "%OUTPUT_FILE%"

REM Alternative simpler command if the above doesn't work
REM ffmpeg -f vnc -framerate %FRAMERATE% -i vnc://%VNC_HOST%:%VNC_PORT% -c:v %CODEC% -preset %QUALITY% -crf 23 "%OUTPUT_FILE%"

echo.
echo Recording stopped.

REM Check if file was created
if exist "%OUTPUT_FILE%" (
    echo [+] Recording saved successfully: %OUTPUT_FILE%
    
    REM Get file size
    for %%A in ("%OUTPUT_FILE%") do set FILE_SIZE=%%~zA
    set /a FILE_SIZE_MB=!FILE_SIZE!/1024/1024
    echo [+] File size: !FILE_SIZE_MB! MB
    
    echo.
    set /p PLAY="Play the recording now? (y/N): "
    if /i "!PLAY!"=="y" (
        echo [+] Opening recording with default media player...
        start "" "%OUTPUT_FILE%"
    )
) else (
    echo [-] Recording file not found. Check for errors above.
)

echo.
echo Recording session completed.
pause

endlocal
