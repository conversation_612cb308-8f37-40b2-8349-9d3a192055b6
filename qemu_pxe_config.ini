[qemu]
; QEMU PXE Boot Configuration
; Edit these values to customize your VM

; Basic VM settings
vm_name=qemu-pxe-test
vm_memory=2048
vm_cpus=2

; QEMU executable path (leave empty for auto-detection)
qemu_path=

; Machine type: pc-i440fx-7.2 (older, compatible) or pc-q35-7.2 (newer, more features)
machine_type=pc-q35-7.2

; CPU type: host (requires acceleration), qemu64 (compatible), or specific CPU model
cpu_type=qemu64

; Acceleration: auto, tcg, hax, whpx, kvm
; auto = detect best available acceleration
acceleration=auto

; Network adapter: rtl8139, e1000, virtio-net-pci
network_adapter=e1000

; Graphics: std, cirrus, vmware, virtio-vga, qxl
graphics=std

; Display: gtk, sdl, vnc, none
display=gtk

; Audio: none, dsound, alsa, pa, oss
audio=dsound

; Boot options
boot_menu=on
splash_time=3000
boot_order=n

; PXE/TFTP settings
tftp_dir=./tftp
bootfile=pxelinux.0

; USB support
usb_enabled=yes
usb_tablet=yes

; Monitor interface
monitor=stdio
no_reboot=yes

; MAC address prefix (leave empty for random generation)
; Format: XX:XX:XX (will be completed with random bytes)
mac_prefix=08:00:27

[advanced]
; Advanced QEMU options (for experienced users)

; BIOS file (leave empty for default)
bios_file=

; Additional QEMU arguments (space-separated)
extra_args=

; Memory balloon (for dynamic memory allocation)
balloon=yes

; RTC settings
rtc_base=localtime
rtc_clock=host

; Enable/disable specific features
enable_kvm=auto
enable_hax=auto
enable_whpx=auto

[network]
; Network configuration for PXE boot

; Network mode: user (NAT), bridge, tap
network_mode=user

; For user mode networking
user_net_options=bootfile=pxelinux.0,tftp=./tftp

; For bridge networking (requires setup)
bridge_name=br0

; DHCP range for user networking (optional)
dhcp_start=*********
dhcp_end=*********

[logging]
; Logging and debugging options

; Log file (leave empty to disable file logging)
log_file=

; Log level: 0=emergency, 1=alert, 2=critical, 3=error, 4=warning, 5=notice, 6=info, 7=debug
log_level=3

; Enable specific log categories (comma-separated)
; Examples: cpu, mmu, int, exec, trace, guest_errors
log_categories=

[compatibility]
; Compatibility settings for different environments

; Windows-specific settings
windows_console=yes
windows_no_console=no

; Performance tuning
thread_count=auto
io_thread=yes

; Security options
sandbox=off
