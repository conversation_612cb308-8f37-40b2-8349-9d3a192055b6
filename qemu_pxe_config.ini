[qemu]
; QEMU PXE Boot Configuration - Anti-Detection Version
; Edit these values to customize your VM

; Basic VM settings
vm_name=stealth-workstation
vm_memory=4096
vm_cpus=4

; QEMU executable path (leave empty for auto-detection)
qemu_path=C:\Program Files\qemu\qemu-system-x86_64.exe

; Machine type: pc-i440fx-7.2 (older, compatible) or pc-q35-7.2 (newer, more features)
machine_type=pc-q35-7.2

; CPU type: host (requires acceleration), qemu64 (compatible), or specific CPU model
cpu_type=qemu64

; Acceleration: auto, tcg, hax, whpx, kvm
; auto = detect best available acceleration
acceleration=tcg

; Network adapter: rtl8139, e1000, virtio-net-pci
network_adapter=e1000

; Graphics: std, cirrus, vmware, virtio-vga, qxl
graphics=std

; Display: gtk, sdl, vnc, none
display=gtk

; Audio: none (disabled for stealth mode)
audio=none

; Boot options
boot_menu=off
splash_time=0
boot_order=n

; PXE settings (real network boot - no local TFTP)
; VM will attempt to PXE boot from actual network

; USB support (disabled for stealth mode)
usb_enabled=no
usb_tablet=no

; Monitor interface
monitor=stdio
no_reboot=yes

; MAC address prefix (Intel OUI for realistic hardware simulation)
; Format: XX:XX:XX (will be completed with random bytes)
mac_prefix=08:00:27

[advanced]
; Advanced QEMU options (for experienced users)

; BIOS file (leave empty for default)
bios_file=

; Additional QEMU arguments (space-separated)
extra_args=-no-hpet -no-acpi

; Memory balloon (disabled for stealth)
balloon=no

; RTC settings
rtc_base=localtime
rtc_clock=host

; Enable/disable specific features
enable_kvm=auto
enable_hax=auto
enable_whpx=auto

[anti_detection]
; Anti-detection and stealth options

; Enable SMBIOS spoofing
smbios_spoofing=yes

; BIOS Information (Type 0)
bios_vendor=American Megatrends Inc.
bios_version=2603
bios_date=04/09/2021

; System Information (Type 1)
system_manufacturer=ASUSTeK COMPUTER INC.
system_product=PRIME Z390-A
system_version=Rev 1.xx
system_serial=auto_generate
system_uuid=550e8400-e29b-41d4-a716-************
system_sku=SKU
system_family=Desktop

; Baseboard Information (Type 2)
board_manufacturer=ASUSTeK COMPUTER INC.
board_product=PRIME Z390-A
board_version=Rev 1.xx
board_serial=auto_generate

; Chassis Information (Type 3)
chassis_manufacturer=Chassis Manufacture
chassis_version=Chassis Version
chassis_serial=Chassis Serial Number
chassis_asset=Asset-1234567890

; Processor Information (Type 4)
cpu_manufacturer=Intel
cpu_version=Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz
cpu_max_speed=3600
cpu_current_speed=3600

; CPU Features
cpu_family=6
cpu_model=158
cpu_stepping=10
cpu_model_id=Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz
vmware_cpuid_freq=off
hypervisor_bit=off

[network]
; Network configuration for real PXE boot

; Network mode: user (NAT), bridge, tap
network_mode=user

; For user mode networking (real PXE boot)
user_net_options=

; For bridge networking (requires setup)
bridge_name=br0

; DHCP range for user networking (optional)
dhcp_start=10.0.2.15
dhcp_end=10.0.2.30

[logging]
; Logging and debugging options

; Log file (leave empty to disable file logging)
log_file=

; Log level: 0=emergency, 1=alert, 2=critical, 3=error, 4=warning, 5=notice, 6=info, 7=debug
log_level=3

; Enable specific log categories (comma-separated)
; Examples: cpu, mmu, int, exec, trace, guest_errors
log_categories=

[compatibility]
; Compatibility settings for different environments

; Windows-specific settings
windows_console=yes
windows_no_console=no

; Performance tuning
thread_count=auto
io_thread=yes

; Security options
sandbox=off
