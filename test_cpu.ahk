#NoEnv
#SingleInstance Force

; Test CPU detection
RunWait, %comspec% /c wmic cpu get NumberOfCores,NumberOfLogicalProcessors /value > cpuinfo_test.txt, , Hide
FileRead, cpuInfo, cpuinfo_test.txt
RegExMatch(cpuInfo, "NumberOfCores=(\d+)", coreMatch)
RegExMatch(cpuInfo, "NumberOfLogicalProcessors=(\d+)", logicalMatch)

if (coreMatch1 && logicalMatch1) {
    ; Use physical cores for VM (not logical/hyperthreaded cores)
    SystemCPUCores := coreMatch1
    SystemCPUThreads := logicalMatch1
    
    ; For VirtualBox, we typically want to use physical cores
    ; But if system has very few cores, we might use logical processors
    if (SystemCPUCores >= 4) {
        VMCPUCount := SystemCPUCores
    } else if (SystemCPUThreads >= 4) {
        VMCPUCount := SystemCPUThreads
    } else {
        VMCPUCount := SystemCPUCores
    }
    
    ; Ensure minimum 1 core and maximum reasonable limit
    if (VMCPUCount < 1)
        VMCPUCount := 1
    else if (VMCPUCount > 16)
        VMCPUCount := 16
} else {
    ; Fallback values
    SystemCPUCores := 4
    SystemCPUThreads := 4
    VMCPUCount := 4
}

; Create output
output := "CPU Detection Test:`n"
output .= "Raw CPU Info:`n" . cpuInfo . "`n"
output .= "Physical Cores: " . SystemCPUCores . "`n"
output .= "Logical Threads: " . SystemCPUThreads . "`n"
output .= "VM CPU Count: " . VMCPUCount . "`n"

FileDelete, cpu_test.log
FileAppend, %output%, cpu_test.log

MsgBox, 64, CPU Test, Physical Cores: %SystemCPUCores%`nLogical Threads: %SystemCPUThreads%`nVM CPU Count: %VMCPUCount%

ExitApp
