# Dynamic VirtualBox Anti-Detection Script

This project provides a dynamic VirtualBox anti-detection system that automatically detects your current system's hardware and applies those specifications to a VirtualBox VM to avoid detection.

## Overview

The original `VirtualBoxCloningScript.cmd` contained hardcoded hardware values. The new `dmidecode.ahk` script dynamically detects your system's actual hardware specifications and applies them directly to a VirtualBox VM.

## Files

- **`dmidecode.ahk`** - Main AutoHotkey script that detects hardware and applies VirtualBox configuration
- **`VirtualBoxCloningScript.cmd`** - Original script with hardcoded values (for reference)
- **`test_dmidecode.cmd`** - Test script to verify the dynamic system works
- **`vm3.ini`** - Configuration file for VirtualBox settings
- **`bin/dmidecode.exe`** - Hardware detection utility
- **`README_Dynamic_VirtualBox.md`** - This documentation

## What the Script Does

### Hardware Detection
1. **DMI/SMBIOS Information**: Uses `dmidecode.exe` to extract:
   - BIOS vendor, version, and date
   - System manufacturer, product, version, serial, UUID, SKU, family
   - Motherboard manufacturer, product, version, serial
   - Chassis information
   - Processor details

2. **Storage Information**: Uses WMIC to detect:
   - Hard disk model, firmware, and serial number
   - System memory size

3. **Network**: Generates random MAC address with Intel OUI (08:00:27)

### VirtualBox Configuration Applied
The script applies all the settings from the original `VirtualBoxCloningScript.cmd` but with dynamic values:

- **Basic VM Settings**: Memory, mouse, graphics controller, VRAM
- **Anti-Detection CPU Settings**: Disables hypervisor presence, sets TSC mode
- **DMI/SMBIOS Configuration**: All BIOS, system, board, and chassis information
- **AHCI Storage**: Hard disk and DVD drive spoofing
- **ACPI Settings**: OEM information
- **Hardware Virtualization**: Optimized settings for performance
- **MAC Address**: Random generation with Intel OUI

## Usage

### Method 1: Direct Execution
```cmd
dmidecode.ahk "VM_NAME"
```

### Method 2: Using Test Script
```cmd
test_dmidecode.cmd "VM_NAME"
```

### Examples
```cmd
dmidecode.ahk "Windows 10"
dmidecode.ahk "Ubuntu 20.04"
test_dmidecode.cmd "My VM"
```

## Requirements

1. **AutoHotkey**: Must be installed to run `.ahk` scripts
2. **Administrator Rights**: Required for hardware detection
3. **VirtualBox**: Must be installed and accessible
4. **Files Required**:
   - `dmidecode.ahk`
   - `bin/dmidecode.exe`
   - `vm3.ini`

## Configuration

Edit `vm3.ini` to customize VirtualBox settings:

```ini
[vbox]
vbox_path=C:\Program Files\Oracle\VirtualBox
vm_memory=4096
vm_mouse=ps2
vm_vga=vboxsvga
vga_vram=128
```

## Output

The script creates `vm3_config_applied.log` with detailed information about:
- Hardware profile detected
- All settings applied
- MAC address generated
- Timestamp of configuration

## Advantages Over Original Script

1. **Dynamic Detection**: No hardcoded values - adapts to any system
2. **Direct Application**: Applies settings directly instead of generating a script
3. **Real Hardware**: Uses actual system specifications for better stealth
4. **Comprehensive**: Includes all original settings plus additional detection
5. **Automated**: Single command execution
6. **Logging**: Detailed log of applied configuration

## Troubleshooting

### Common Issues

1. **"dmidecode.exe failed"**
   - Run as Administrator
   - Ensure `bin/dmidecode.exe` exists and is compatible

2. **"VBoxManage.exe not found"**
   - Check VirtualBox installation path in `vm3.ini`
   - Verify VirtualBox is properly installed

3. **"VM not found"**
   - Ensure the VM name exists in VirtualBox
   - Check VM name spelling and case sensitivity

### Debug Information

Check `vm3_config_applied.log` for:
- Hardware detection results
- Applied configuration values
- Any error messages

## Security Note

This script is designed for legitimate testing and development purposes. Ensure you comply with all applicable laws and terms of service when using virtualization anti-detection techniques.
