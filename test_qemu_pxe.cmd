@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM QEMU PXE Boot Test Script
REM Creates a VM with no disk/cdrom that boots from PXE network
REM ============================================================================

echo ============= QEMU PXE Boot Test =============
echo.
echo This script will create a QEMU VM that:
echo - Has no hard disk or CD-ROM attached
echo - Boots from PXE (network boot)
echo - Uses hardware specs similar to your host system
echo.

REM Configuration variables
set VM_NAME=qemu-pxe-test
set VM_MEMORY=2048
set VM_CPUS=2
set QEMU_PATH=qemu-system-x86_64.exe

REM Check if QEMU is available in PATH
where %QEMU_PATH% >nul 2>&1
if errorlevel 1 (
    echo Error: QEMU not found in PATH
    echo Please install QEMU or add it to your PATH
    echo Download from: https://www.qemu.org/download/
    pause
    exit /b 1
)

echo [+] Found QEMU: %QEMU_PATH%
echo.

REM Generate random MAC address with common vendor prefix (52:54:00 - QEMU default)
set /a "mac1=%random% %% 256"
set /a "mac2=%random% %% 256"
set /a "mac3=%random% %% 256"
call :tohex !mac1! hex1
call :tohex !mac2! hex2  
call :tohex !mac3! hex3
set MAC_ADDRESS=52:54:00:!hex1!:!hex2!:!hex3!

echo [+] Generated MAC address: %MAC_ADDRESS%
echo.

REM Basic QEMU command for PXE boot
set QEMU_CMD=%QEMU_PATH%
set QEMU_CMD=%QEMU_CMD% -name "%VM_NAME%"
set QEMU_CMD=%QEMU_CMD% -m %VM_MEMORY%
set QEMU_CMD=%QEMU_CMD% -smp %VM_CPUS%
set QEMU_CMD=%QEMU_CMD% -machine pc-i440fx-7.2,accel=tcg
set QEMU_CMD=%QEMU_CMD% -cpu qemu64
set QEMU_CMD=%QEMU_CMD% -rtc base=localtime,clock=host
set QEMU_CMD=%QEMU_CMD% -boot order=n
set QEMU_CMD=%QEMU_CMD% -netdev user,id=net0,bootfile=pxelinux.0
set QEMU_CMD=%QEMU_CMD% -device rtl8139,netdev=net0,mac=%MAC_ADDRESS%
set QEMU_CMD=%QEMU_CMD% -vga std
set QEMU_CMD=%QEMU_CMD% -display gtk
set QEMU_CMD=%QEMU_CMD% -no-reboot
set QEMU_CMD=%QEMU_CMD% -monitor stdio

echo ============================================================================
echo QEMU Configuration:
echo VM Name: %VM_NAME%
echo Memory: %VM_MEMORY% MB
echo CPUs: %VM_CPUS%
echo MAC Address: %MAC_ADDRESS%
echo Boot Order: Network (PXE) only
echo Network: User mode (NAT) with PXE support
echo Storage: None (no disk/cdrom attached)
echo ============================================================================
echo.

echo Starting QEMU VM...
echo.
echo Note: The VM will attempt to PXE boot from the network.
echo If no PXE server is available, it will show a boot failure message.
echo Press Ctrl+C in this window to stop the VM.
echo.

REM Start QEMU
echo Command: %QEMU_CMD%
echo.
%QEMU_CMD%

echo.
echo VM has stopped.
pause
goto :eof

REM Function to convert decimal to hex
:tohex
set /a "dec=%1"
set "hex="
set "digits=0123456789ABCDEF"
if %dec% equ 0 (
    set "hex=00"
    goto :tohex_end
)
:tohex_loop
if %dec% equ 0 goto :tohex_end
set /a "remainder=dec %% 16"
set /a "dec=dec / 16"
call set "digit=%%digits:~%remainder%,1%%"
set "hex=%digit%%hex%"
goto :tohex_loop
:tohex_end
if "%hex:~1,1%"=="" set "hex=0%hex%"
set "%2=%hex%"
goto :eof

endlocal
