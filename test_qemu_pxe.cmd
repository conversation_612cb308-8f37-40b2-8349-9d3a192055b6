@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM QEMU PXE Boot Test Script - Anti-Detection Version
REM Creates a VM with no disk/cdrom that boots from PXE network
REM Includes anti-detection features to hide VM from OS detection
REM ============================================================================

echo ============= QEMU PXE Boot Test - Anti-Detection =============
echo.
echo This script will create a QEMU VM that:
echo - Has no hard disk or CD-ROM attached
echo - Boots from PXE (network boot)
echo - Uses anti-detection techniques to hide VM presence
echo - No USB or audio devices (stealth mode)
echo.

REM Configuration variables
set VM_NAME=qemu-pxe-stealth
set VM_MEMORY=2048
set VM_CPUS=2
set QEMU_PATH=C:\Program Files\qemu\qemu-system-x86_64.exe

REM Check if QEMU is available
if not exist "%QEMU_PATH%" (
    echo Error: QEMU not found at %QEMU_PATH%
    echo Please install QEMU or update the path
    echo Download from: https://www.qemu.org/download/
    pause
    exit /b 1
)

echo [+] Found QEMU: %QEMU_PATH%
echo.

REM Generate random MAC address with Intel OUI (like real hardware)
set /a "mac1=%random% %% 256"
set /a "mac2=%random% %% 256"
set /a "mac3=%random% %% 256"
call :tohex !mac1! hex1
call :tohex !mac2! hex2
call :tohex !mac3! hex3
set MAC_ADDRESS=08:00:27:!hex1!:!hex2!:!hex3!

echo [+] Generated MAC address: %MAC_ADDRESS%
echo.

REM Anti-Detection QEMU command for PXE boot
set QEMU_CMD=%QEMU_PATH%
set QEMU_CMD=%QEMU_CMD% -name "%VM_NAME%"
set QEMU_CMD=%QEMU_CMD% -m %VM_MEMORY%
set QEMU_CMD=%QEMU_CMD% -smp %VM_CPUS%
set QEMU_CMD=%QEMU_CMD% -machine pc-q35-7.2,accel=tcg,kernel_irqchip=on,hpet=off,acpi=off
set QEMU_CMD=%QEMU_CMD% -cpu qemu64,family=6,model=158,stepping=2
set QEMU_CMD=%QEMU_CMD% -cpu qemu64,model_id="Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz"
set QEMU_CMD=%QEMU_CMD% -cpu qemu64,vmware-cpuid-freq=off,hypervisor=off
set QEMU_CMD=%QEMU_CMD% -rtc base=localtime,clock=host

REM SMBIOS spoofing to hide VM signatures
set QEMU_CMD=%QEMU_CMD% -smbios type=0,vendor="American Megatrends Inc.",version="F12",date="01/16/2019"
set QEMU_CMD=%QEMU_CMD% -smbios type=1,manufacturer="ASUS",product="PRIME Z390-A",version="Rev 1.xx",serial="System Serial Number"
set QEMU_CMD=%QEMU_CMD% -smbios type=2,manufacturer="ASUSTeK COMPUTER INC.",product="PRIME Z390-A",version="Rev 1.xx",serial="BSN12345678901234567"
set QEMU_CMD=%QEMU_CMD% -smbios type=3,manufacturer="Chassis Manufacture",version="Chassis Version",serial="Chassis Serial Number"

REM Network and boot configuration - Real PXE boot
set QEMU_CMD=%QEMU_CMD% -boot order=n,menu=off
set QEMU_CMD=%QEMU_CMD% -netdev user,id=net0
set QEMU_CMD=%QEMU_CMD% -device e1000,netdev=net0,mac=%MAC_ADDRESS%

REM Display without VM-specific features
set QEMU_CMD=%QEMU_CMD% -vga std
set QEMU_CMD=%QEMU_CMD% -display gtk
set QEMU_CMD=%QEMU_CMD% -no-reboot
set QEMU_CMD=%QEMU_CMD% -monitor stdio

REM VM-specific features disabled via machine properties above

echo ============================================================================
echo QEMU Anti-Detection Configuration:
echo VM Name: %VM_NAME%
echo Memory: %VM_MEMORY% MB
echo CPUs: %VM_CPUS%
echo MAC Address: %MAC_ADDRESS%
echo Boot Order: Network (PXE) only
echo Network: User mode (NAT) - Real PXE boot
echo Storage: None (no disk/cdrom attached)
echo Anti-Detection: SMBIOS spoofing, CPU masking, no VM signatures
echo ============================================================================
echo.

echo Starting stealth QEMU VM...
echo.
echo Note: The VM will attempt real PXE boot from the network.
echo If no PXE server is available on your network, it will show boot failure.
echo This simulates a real PC trying to network boot.
echo Press Ctrl+C in this window to stop the VM.
echo.

REM Start QEMU
echo Command: %QEMU_CMD%
echo.
%QEMU_CMD%

echo.
echo VM has stopped.
pause
goto :eof

REM Function to convert decimal to hex
:tohex
set /a "dec=%1"
set "hex="
set "digits=0123456789ABCDEF"
if %dec% equ 0 (
    set "hex=00"
    goto :tohex_end
)
:tohex_loop
if %dec% equ 0 goto :tohex_end
set /a "remainder=dec %% 16"
set /a "dec=dec / 16"
call set "digit=%%digits:~%remainder%,1%%"
set "hex=%digit%%hex%"
goto :tohex_loop
:tohex_end
if "%hex:~1,1%"=="" set "hex=0%hex%"
set "%2=%hex%"
goto :eof

endlocal
